import { Component, OnInit, Output, EventEmitter, TemplateRef } from '@angular/core';
import { NgbOffcanvas } from '@ng-bootstrap/ng-bootstrap';
import { EventService } from "../../../services/event/event.service";
import { TemaService } from "../../../services/tema/tema.service";

@Component({
    selector: 'app-rightsidebar',
    templateUrl: './rightsidebar.component.html',
    styleUrls: ['./rightsidebar.component.scss']
})

/**
 * Right Sidebar component
 */
export class RightsidebarComponent implements OnInit {

    temaMudado: boolean = false;
    opcoesMenuLateral: any;
    temaLocal: any;
    attribute: any;
    grd: any;

    @Output() settingsButtonClicked = new EventEmitter();

    constructor(private eventService: EventService, private offcanvasService: NgbOffcanvas, 
                private temaService: TemaService) {
        this.temaService = temaService;
    }

    ngOnInit(): void {
        //this.opcoesMenuLateral = this.temaService.getTemaLocal();
        //this.temaLocal = this.opcoesMenuLateral;
    }

    // When the user clicks on the button, scroll to the top of the document
    topFunction() {
        document.body.scrollTop = 0;
        document.documentElement.scrollTop = 0;
    }

    /*ngAfterViewInit() {
        setTimeout(() => {
            this.attribute = '';
            this.attribute = document.documentElement.getAttribute('data-layout');
            if (this.attribute == 'vertical') {
                const vertical = document.getElementById('customizer-layout01');
                if (vertical != null)
                    vertical.setAttribute('checked', 'true');
            }
            if (this.attribute == 'horizontal') {
                const horizontal = document.getElementById('customizer-layout02');
                if (horizontal != null)
                    horizontal.setAttribute('checked', 'true');
            }
            if (this.attribute == 'twocolumn') {
                const Twocolumn = document.getElementById('customizer-layout03');
                if (Twocolumn != null)
                    Twocolumn.setAttribute('checked', 'true');
            }
        }, 0);
    }

    saveTema() {
        this.temaService.saveTema();
        this.temaMudado = false;
    }

    changeLayoutColor(layoutColor: string) {
        this.temaService.changeLayoutColor(layoutColor);
        this.opcoesMenuLateral.layoutColor = layoutColor;
        this.opcoesMenuLateral.sidebarColor = layoutColor;
        this.opcoesMenuLateral.topbarColor = layoutColor;
        this.temaMudado = true;
    }

    changeLayoutStyle(layoutStyle: string) {
        this.temaService.changeLayoutStyle(layoutStyle);
        this.opcoesMenuLateral.layoutStyle = layoutStyle;
        this.temaMudado = true;
    }

    changeLayoutWidth(layoutWidth: string) {
        this.temaService.changeLayoutWidth(layoutWidth);
        this.opcoesMenuLateral.layoutWidth = layoutWidth;
        this.temaMudado = true;
    }

    changeLayoutPosition(layoutPosition: string) {
        this.temaService.changeLayoutPosition(layoutPosition);
        this.opcoesMenuLateral.layoutPosition = layoutPosition;
        this.temaMudado = true;
    }

    changeTopbarColor(topbarColor: string) {
        this.temaService.changeTopbarColor(topbarColor);
        this.opcoesMenuLateral.topbarColor = topbarColor;
        this.temaMudado = true;
    }

    changeSidebarSize(sidebarSize: string) {
        this.temaService.changeSidebarSize(sidebarSize);
        this.opcoesMenuLateral.sidebarSize = sidebarSize;
        this.temaMudado = true;
    }

    changeSidebarView(sidebarView: string) {
        this.temaService.changeSidebarView(sidebarView);
        this.opcoesMenuLateral.sidebarView = sidebarView;
        this.temaMudado = true;
    }

    changeSidebarColor(sidebarColor: string) {
        this.temaService.changeSidebarColor(sidebarColor);
        this.opcoesMenuLateral.sidebarColor = sidebarColor;
        this.temaMudado = true;
    }

    // Add Active Class
    addActive(grdSidebar: any) {
        this.grd = grdSidebar;
        document.documentElement.setAttribute('data-sidebar', grdSidebar)
        document.getElementById('collapseBgGradient')?.classList.toggle('show');
        document.getElementById('collapseBgGradient1')?.classList.add('active');
    }

    // Remove Active Class
    removeActive() {
        this.grd = '';
        document.getElementById('collapseBgGradient1')?.classList.remove('active');
        document.getElementById('collapseBgGradient')?.classList.remove('show');
    }

    //  Filter Offcanvas Set
    openEnd(content: TemplateRef<any>) {
        this.offcanvasService.open(content, {position: 'end'});
    }*/

}
