import { Component, OnInit, EventEmitter, Output, ViewChild, ElementRef } from '@angular/core';
import { AppGlobals } from "../../../app.globals";
import { MenuModuloResponse } from "../../../models/usuario/usuario-menus";


@Component({
    selector: 'app-two-column-sidebar', // filho do app-two-column
    templateUrl: './two-column-sidebar.component.html',
    styleUrls: ['./two-column-sidebar.component.scss']
})
export class TwoColumnSidebarComponent implements OnInit {

    logoEstendida = AppGlobals.LOGO_ESTENDIDA;
    logoSm = AppGlobals.LOGO_SM;
    menu: any;
    toggle: any = true;
    menuModulos!: MenuModuloResponse[];

    @ViewChild('sideMenu') sideMenu!: ElementRef;
    @Output() mobileMenuButtonClicked = new EventEmitter();

    constructor() {
    }

    ngOnInit(): void {
        // Menu Items
        this.menuModulos = JSON.parse(<string>localStorage.getItem('MenuModulos'));
    }

    /***
     * Activate drop down set
     */
    ngAfterViewInit() {
        //this.reativarMenuClicado();
        document.body.classList.add('twocolumn-panel');
    }

    // Abre as opcoes de um modulo ao clicar em seu icone
    toggleMenuModulo(event: any) {

        let estavaAtivo: boolean = false;
        // event.target = icone do menuModulo clicado,
        // mostra a lista de menuPais deste menuModulo
        if(event.target.classList.contains("active")){
            estavaAtivo = true;
        }
        
        // faz o menu de navegacao aparecer caso ele esteja fechado
        if (document.body.classList.contains('twocolumn-panel')) {
            // pro menu aparecer é só remover essa classe do body
            // ela é adicionada novamente qnd clicar em um icone de menuModulo ou no botao de menu mobile
            document.body.classList.remove('twocolumn-panel');
        } else if(estavaAtivo) {
            document.body.classList.add('twocolumn-panel');
        }

        // elemento ao qual a estrutura da sidebar do twocolumn e seu menu de navegacao que abre/fecha estao dentro
        const ulTwoColumnSidebarNav = document.getElementById("two-column-menu");
        if (ulTwoColumnSidebarNav) {
            // lista de icones de menuModulos (o primeiro da lista é a logo da empresa)
            const menuModuloIcones = Array.from(ulTwoColumnSidebarNav.getElementsByTagName("a"));
            // lista dos icones de menuModulos que estao ativos (apenas um deve estar ativo por vez)
            let menuModuloIconesAtivos = menuModuloIcones.filter((x: any) => x.classList.contains("active"));
            menuModuloIconesAtivos.forEach((item: any) => {
                // limpa a selecao de todos os icones de menuModulos
                item.classList.remove("active");
            });
        }

        // attr.idMenuModulo="{{menuModulo.idMenu}}
        // id do modulo q acabou de ser clicado (pegado a partir do icone)
        let idMenuModuloClicado = event.target.getAttribute('idMenuModulo');

        // div do menuModulo que contem seus menuPais, id="menuPaiLista{{menuModulo.idMenu}}"
        let divMenuPaisDoModulo = document.getElementById('menuPaiLista'+idMenuModuloClicado) as any;

        // menuPais que aparecem ao lado quando clicar no icone de um menuModulo
        // limpar qualquer um para depois mostrar apenas o do menu modulo em questao
        // id="navbar-nav"
        let menuPais = Array.from(document.querySelectorAll('#navbar-nav .show'));
        menuPais.forEach((menuPai: any) => {
            menuPai.classList.remove('show');
        });

        // adiciona a classe show à div do modulo caso ele exista
        (divMenuPaisDoModulo) ? divMenuPaisDoModulo.classList.add('show') : null;


        // muda a setinha dos menuPais para a aparencia "fechada"
        let menuPaisAbertos = Array.from(document.querySelectorAll('.menu-dropdown .nav-link'));
        menuPaisAbertos.forEach((menuPai: any) => {
            menuPai.setAttribute('aria-expanded', "false");
        });

        if(!estavaAtivo){
            event.target.classList.add("active")
        }
    }

    //ao clicar em um menuPai (exemplo: Cadastros)
    toggleMenuPai(event: any) {
        // fihlos do menu pai = menuPaiClicado.nextElementSibling as any;
        let menuPaiClicado = event.target.closest('a.nav-link');

        // esconder todos os menuFilhos de quaisquer menuPai
        let menuFilhosAbertos = Array.from(document.querySelectorAll('.menu-dropdown .show'));
        menuFilhosAbertos.forEach((menuFilho: any) => {
            menuFilho.classList.remove('show');
        });

        // se o menuPai clicado estiver ativo
        if (menuPaiClicado.getAttribute("aria-expanded") == "true") {
            // entao desativa
            menuPaiClicado.setAttribute('aria-expanded', "false");
        }
        // se nao, ele esta ativo
        else {
            // entao desabilita todos que estao abertos
            // o aria-expanded também afeta o ícone de setinha ao lado de um menuPai
            let menuPaisAbertos = Array.from(document.querySelectorAll('.menu-dropdown .nav-link'));
            menuPaisAbertos.forEach((menuPai: any) => {
                menuPai.setAttribute('aria-expanded', "false");
            });

            // e ativa o que foi clicado
            menuPaiClicado.setAttribute("aria-expanded", "true")
            event.target.nextElementSibling.classList.toggle("show");
        }
    };

    updateActive(event: any) {
        const ul = document.getElementById("navbar-nav");
        if (ul) {
            const items = Array.from(ul.querySelectorAll("a.nav-link.active"));
            this.removeActivation(items);
        }
        this.ativarPaisEFilhos(event.target);
    }

    // remove active items of two-column-menu
    removeActivation(items: any) {
        items.forEach((item: any) => {
            if (item.classList.contains("menu-link")) {
                if (!item.classList.contains("active")) {
                    item.setAttribute("aria-expanded", false);
                }
                item.nextElementSibling.classList.remove("show");
            }
            if (item.classList.contains("nav-link")) {
                if (item.nextElementSibling) {
                    item.nextElementSibling.classList.remove("show");
                }
                item.setAttribute("aria-expanded", false);
            }
            item.classList.remove("active");
        });
    }

    ativarIconeMenuModulo(id: any) {
        var menuModuloIcone = document.querySelector('[idMenuModulo='+id+']');
        if (menuModuloIcone !== null) {
            menuModuloIcone.classList.add("active");
        }
    }

    ativarPaisEFilhos(item: any) { // navbar-nav menu add active
        item.classList.add("active");
        let listaMenuFilhos = item.closest(".collapse.menu-dropdown");
        if (listaMenuFilhos) {
            // to set aria expand true remaining
            listaMenuFilhos.classList.add("show");
            
            let listaMenuPais = listaMenuFilhos.parentElement;
            
            //ativa o menuPai da lista
            listaMenuPais.children[0].classList.add("active");
            listaMenuPais.children[0].setAttribute("aria-expanded", "true");
            
            //lista de menuPais
            if (listaMenuPais.closest(".collapse.menu-dropdown")) {
                listaMenuPais.closest(".collapse").classList.add("show");
                if (listaMenuPais.closest(".collapse").previousElementSibling)
                    listaMenuPais.closest(".collapse").previousElementSibling.classList.add("active");
            }
            this.ativarIconeMenuModulo(listaMenuFilhos.getAttribute("idMenuModulo"));
            return false;
        }
        return false;
    }

    // Reativa o menu após ter clicado em algum de seus links e ter passado de página, 
    // pois o menu fecha quando isso acontece
    reativarMenuClicado() {
        //todo reativar menu 
        document.body.classList.remove('twocolumn-panel');
        //rota a partir da url
        const rotaAtual = window.location.pathname;
        //lista com os menuPais e menuFilhos
        const listaDeMenuLinks = document.getElementById("navbar-nav");
        if (listaDeMenuLinks) {
            const itens = Array.from(listaDeMenuLinks.querySelectorAll("a.nav-link"));
            
            let itensAtivos = itens.filter((x: any) => x.classList.contains("active"));
            this.removeActivation(itensAtivos);
            
            let menuAbertoDessaRota = itens.find((x: any) => {
                return x.pathname === rotaAtual;
            });
            
            if (menuAbertoDessaRota) {
                this.ativarPaisEFilhos(menuAbertoDessaRota);
            } else {
                var id = rotaAtual.replace("/", "");
                if (id) document.body.classList.add('twocolumn-panel');
                this.ativarIconeMenuModulo(id)
            }
        }
    }


    //ao clicar em um menuPai (exemplo: Cadastros)
    toggleMenuPaiDeixandoMaisDeUmAtivo(event: any) {
        // fihlos do menu pai = menuPaiClicado.nextElementSibling as any;
        let menuPaiClicado = event.target.closest('a.nav-link');

        // esconder todos os menuFilhos de quaisquer menuPai
        // menos os do menuPaiClicado
        let menuFilhosAbertos = Array.from(document.querySelectorAll('.menu-dropdown .show :not(' + menuPaiClicado.id + ')'));
        menuFilhosAbertos.forEach((menuFilho: any) => {
            menuFilho.classList.remove('show');
        });

        // o aria-expanded também afeta o ícone de setinha ao lado de um menuPai
        let menuPaisAbertos = Array.from(document.querySelectorAll('.menu-dropdown .nav-link :not(' + menuPaiClicado.id + ')'));
        menuPaisAbertos.forEach((menuPai: any) => {
            menuPai.setAttribute('aria-expanded', "false");
        });

        if (event.target && event.target.nextElementSibling) {
            if (menuPaiClicado.getAttribute("aria-expanded") == "true")
                menuPaiClicado.setAttribute("aria-expanded", "false")
            else
                menuPaiClicado.setAttribute("aria-expanded", "true")

            event.target.nextElementSibling.classList.toggle("show");
        }
    };
}
