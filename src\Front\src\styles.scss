/* You can add global styles to this file, and also import other style files */

//---------------------------------------------------------------------------------------------

// Angular Material Dialog Override
.mat-dialog-container,
.mat-mdc-dialog-container {
    background-color: var(--vz-card-bg) !important;
    background: var(--vz-card-bg) !important;
    color: var(--vz-body-color) !important;
}

// Garantir que o tema seja aplicado imediatamente
html {
    // Tema padrão (light)
    --vz-body-bg: #f3f3f9;
    --vz-card-bg: #ffffff;
    --vz-body-color: #495057;
}

// Tema escuro
html[data-layout-mode="dark"] {
    --vz-body-bg: #1a1d21 !important;
    --vz-card-bg: #212529 !important;
    --vz-body-color: #ced4da !important;
}

// Aplicar tema no body imediatamente
body {
    background-color: var(--vz-body-bg) !important;
    color: var(--vz-body-color) !important;
    transition: background-color 0.3s ease, color 0.3s ease;
}

//Grid
.page-container-box {
    padding: 10px;
    background-color: var(--vz-body-bg);
    border-bottom: 1px solid none;
    border-top: 1px solid none;
}

.mat-table {
    background-color: var(--vz-card-bg) !important;
    font-family: var(--vz-body-font-family) !important;

    td {
        padding: 0.75rem 0.65rem !important;
        //border-right: 1px solid var(--vz-input-border);

        &:last-child {
            width: 100%;
            //border-right: none;
        }
    }

    th {
        padding: 1rem 0.65rem !important;
        //border-right: 1px solid var(--vz-input-border);
        &:last-child {
            //border-right: none;
        }
    }

    margin-bottom: 0 !important;
}

//Cor das bordas entre as linhas
.mat-row,
.mat-header-row,
.mat-footer-row,
th.mat-header-cell,
td.mat-cell,
td.mat-footer-cell {
    border-bottom-color: inherit !important;
}

.mat-select-arrow, //setinha do select de ipp,
.mat-option-text, //texto da opcao mostrada de itens por pag
.mat-sort-header-arrow, //setinha de sort das colunas
.mat-select-value-text, //texto selecionado do ipp
.mat-paginator-container, //div do paginator
.mat-select-panel,
.mat-cell, //linhas da grid
.mat-footer-cell, //linhas da grid
.mat-sort-header-content //Textos das colunas
{
    color: var(--vz-dark) !important;
    font-size: 14px !important;
}

.mat-icon-button {
    color: var(--vz-dark) !important;
    background-color: transparent;

    &:hover {
        background-color: var(--vz-topbar-search-bg);
    }
}

.mat-icon-button.mat-button-disabled {
    color: var(--vz-input-border) !important;

    &:hover {
        background-color: transparent;
    }
}

//bg das opcoes nao ativadas 
.mat-select-panel .mat-optgroup-label, .mat-select-panel .mat-option {
    background-color: var(--vz-header-bg);
}

//bg das opcoes ao hover
.mat-option:hover:not(.mat-option-disabled), .mat-option:focus:not(.mat-option-disabled) {
    background: var(--vz-topbar-user-bg) !important;
}

//bg da opcao ativada
.mat-select-panel .mat-option.mat-selected:not(.mat-option-multiple) {
    background: var(--vz-input-border) !important;
}

//onde apareceo  numero de items por pg atual
.mat-paginator-container {
    background-color: var(--vz-card-bg);
}

//texto das opcoes n selecionadas
.mat-primary .mat-option.mat-selected:not(.mat-option-disabled) {
    color: var(--vz-link-color)
}

//---------------------------------------------------------------------------------------------

// Tira animacao do botao de ir p topo
// Tambem muda sua posicao
#back-to-top {
    right: 26px !important;
    bottom: 40px !important;

    &:hover {
        animation: none !important;
    }
}

//---------------------------------------------------------------------------------------------

// Tira borda de 1 px do menu Customizador de Temas
.offcanvas-end {
    border-left: none !important;
}

//---------------------------------------------------------------------------------------------

// Para de deslocar a logo pra direita qnd usa menu vertical e tamanho pequeno com hover
.navbar-brand-box {
    padding: 0 !important;
}

[data-layout="vertical"] {
    &[data-sidebar-size="sm-hover"] {
        .navbar-menu {
            &:hover {
                .navbar-brand-box {
                    padding: 0 1.3rem !important;
                }
            }
        }
    }

    &[data-sidebar-size="sm-hover-active"] {
        .navbar-brand-box {
            padding: 0 1.3rem !important;
        }
    }
}

//---------------------------------------------------------------------------------------------

//Componente do autocomplete com validação
.ng-autocomplete.form-control {
    max-width: 100% !important;
    padding: 0 28px 0 0 !important;
}

//Tira a borda dupla qnd se usa validacao no autocomplete
.ng-autocomplete.form-control .autocomplete-container .input-container input {
    border: none !important;
}

// Drop do autocomplete
.autocomplete-container .input-container input {
    color: var(--vz-body-color) !important;
    border: none !important;
}

.close {
    color: var(--vz-body-color) !important;
    background: none !important;
}

.autocomplete-container .suggestions-container ul {
    background-color: var(--vz-dropdown-bg);
}

.autocomplete-container .suggestions-container ul li a, b {
    color: var(--vz-body-color) !important;
}

//---------------------------------------------------------------------------------------------

//Tira borda do botao de olho da senha no login
#password-addon {
    box-shadow: none !important;
}

//Tira borda do botao de fixar menu vertical com hover
#vertical-hover {
    box-shadow: none !important;
}

//---------------------------------------------------------------------------------------------

//Coloca scroll no hover que tem os filhos de um menuPai quando é utilizado vertical pequeno 
[data-layout="vertical"][data-sidebar-size="sm"] .navbar-menu .navbar-nav .nav-item:hover > .menu-dropdown {
    .sub-menu {
        max-height: calc(100vh - 200px);
        overflow-y: auto;
    }
}

//Mantem os itens do menu vertical pequeno na tela 
//todo talvez isso esconda alguns dos modulos no menu vertical pequeno se tiverem muitos
[data-sidebar-size="sm"] {
    #scrollbar {
        position: fixed !important;
    }
}

//---------------------------------------------------------------------------------------------

//Tira a coloração padrão do autofill do chrome
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active, {
    -webkit-text-fill-color: var(--vz-dark);
    -webkit-box-shadow: 0 0 0 50px var(--vz-topbar-user-bg) inset, 0 0 8px var(--vz-topbar-user-bg) !important;
}