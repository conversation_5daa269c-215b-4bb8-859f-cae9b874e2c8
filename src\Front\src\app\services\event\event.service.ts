import { Injectable } from '@angular/core';
import { Subject, Subscription } from 'rxjs';
import { filter, map } from 'rxjs/operators';

@Injectable({
    providedIn: 'root'
})
export class EventService {
    private events = new Subject<any>();

    broadcast(name: string, data?: any) {
        this.events.next({ name, data });
    }

    subscribe(name: string, callback: (data: any) => void): Subscription {
        return this.events.pipe(
            filter(event => event.name === name),
            map(event => event.data)
        ).subscribe(callback);
    }
}
