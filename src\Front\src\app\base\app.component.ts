import { Component } from '@angular/core';

@Component({
    selector: 'app-root',
    templateUrl: './app.component.html',
    styleUrls: ['./app.component.scss']
})
export class AppComponent {
    title = 'ProjetoPadrao';

    constructor() {
        this.createTemplateLink();
    }

    createTemplateLink() {
        if (document.getElementById('template-tema')) {
            return;
        } else {
            const head = document.getElementsByTagName('head')[0];
            let link = document.createElement('link');
            link.id = 'template-tema';
            link.rel = 'stylesheet';
            link.href = '';
            head.appendChild(link);
        }
    }
}