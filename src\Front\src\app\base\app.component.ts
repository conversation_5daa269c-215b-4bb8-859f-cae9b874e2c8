import { Component } from '@angular/core';

@Component({
    selector: 'app-root',
    templateUrl: './app.component.html',
    styleUrls: ['./app.component.scss']
})
export class AppComponent {
    title = 'ProjetoPadrao';

    constructor() {
        this.createTemplateLink();
        this.applyThemeEarly();
    }

    createTemplateLink() {
        if (document.getElementById('template-tema')) {
            return;
        } else {
            const head = document.getElementsByTagName('head')[0];
            let link = document.createElement('link');
            link.id = 'template-tema';
            link.rel = 'stylesheet';
            link.href = '';
            head.appendChild(link);
        }
    }

    applyThemeEarly() {
        // Aplicar tema salvo no localStorage imediatamente
        const temaString = localStorage.getItem('Tema');
        if (temaString) {
            try {
                const tema = JSON.parse(temaString);
                console.log('AppComponent: Aplicando tema precocemente', tema);

                // Garantir que os atributos estejam aplicados
                if (tema.layoutColor) {
                    document.documentElement.setAttribute('data-layout-mode', tema.layoutColor);
                }
                if (tema.layoutStyle) {
                    document.documentElement.setAttribute('data-layout', tema.layoutStyle);
                }
                if (tema.layoutPosition) {
                    document.documentElement.setAttribute('data-layout-position', tema.layoutPosition);
                }
                if (tema.layoutWidth) {
                    document.documentElement.setAttribute('data-layout-width', tema.layoutWidth);
                }
                if (tema.topbarColor) {
                    document.documentElement.setAttribute('data-topbar', tema.topbarColor);
                }
                if (tema.sidebarColor) {
                    document.documentElement.setAttribute('data-sidebar', tema.sidebarColor);
                }
                if (tema.sidebarSize) {
                    document.documentElement.setAttribute('data-sidebar-size', tema.sidebarSize);
                }
                if (tema.sidebarView) {
                    document.documentElement.setAttribute('data-layout-style', tema.sidebarView);
                }
            } catch (e) {
                console.error('Erro ao aplicar tema no AppComponent:', e);
            }
        }
    }
}