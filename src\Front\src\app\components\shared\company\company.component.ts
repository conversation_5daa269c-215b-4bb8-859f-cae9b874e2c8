import { Component, OnInit } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { ETypeActionButton } from 'src/app/models/enums/ETypeActionButton';
import { ETypeFilter } from 'src/app/models/enums/ETypeFilter';
import { ActionRow } from 'src/app/models/grid/ActionRow';
import { GridOptions } from 'src/app/models/grid/gridOptions';
import { EventService } from 'src/app/services/event/event.service';
import { GridService } from 'src/app/services/grid/grid.service';

@Component({
  selector: 'app-icon-selector-dialog',
  templateUrl: './company.component.html',
  styleUrls: ['./company.component.scss']
})
export class CompanyDialogComponent {
   
    gridOptions: GridOptions;
    // breadCrumbItems!: Array<{}>;
    hasItems: boolean = true;  
    

    selectIcon(Data: any) {
      this.dialogRef.close(Data);
    }

    constructor(private gridService: GridService, private eventService: EventService, public dialogRef: MatDialogRef<CompanyDialogComponent>) {
        // this.breadCrumbItems = [
        //     {label: 'Dashboards'},
        //     {label: 'Usuário', active: true}
        // ];

        this.eventService.subscribe('gridActionRow', (action: ActionRow) => {
            if (action.Action.TypeActionButton == ETypeActionButton.Selecionar)
              this.selectIcon(action.Data)
        });


        this.eventService.subscribe('gridTotalDadosCarregados', (total: number) => {
            this.hasItems = total > 0;
        });


        this.gridOptions = {
            Parametros: {
                Controller: 'Empresa',
                Metodo: 'ConsultarGrid',
                PaginatorSizeOptions: [10, 50, 100],
                PageSize: 10,
            },
            Colunas: this.getColunas()
        }
    }

    recarregarGrid() {
        this.gridService.recarregarGrid();
    }

    getAtivoCellTemplate(data: any): string {
        const value = data.ativo;
        return value === 1 
            ? '<span class="badge bg-success">Ativo</span>' 
            : '<span class="badge bg-danger">Inativo</span>';
    }

    getColunas() {
        return  [{
            Field: 'Ações',
            DisplayName: 'Ações',
            Width: 0,
            CellTemplate: undefined,
            Type: ETypeFilter.none,
            Filter: false,
            ServerField: '',
            ActionButton: [
                {
                    TypeActionButton: ETypeActionButton.Selecionar,
                    TypeButton: 0,
                    ParametrosAction: {
                        Conteudo: '<i class="mdi mdi-check-bold"></i>',
                        ClassProperty: 'btn btn-primary btn-sm',
                        Disabled: false,
                        Hidden: false,
                        Target: undefined,
                        Href: undefined,
                        Tooltip: 'Selecionar'
                    }
                }
            ]
        },
            {
                Field: 'id',
                DisplayName: 'Cód',
                CellTemplate: undefined,
                Width: 0,
                ActionButton: undefined,
                Type: ETypeFilter.Number,
                Filter: true,
                ServerField: 'Id'
            },
            {
                Field: 'razaoSocial',
                DisplayName: 'Razao Social',
                Width: 3,
                CellTemplate: undefined,
                ActionButton: undefined,
                Type: ETypeFilter.String,
                Filter: false,
                ServerField: 'RazaoSocial',
                Base64: 'foto'
            },
            {
                Field: 'cnpj',
                DisplayName: 'CNPJ',
                CellTemplate: undefined,
                Width: 2,
                ActionButton: undefined,
                Type: ETypeFilter.String,
                ServerField: 'Cnpj',
                Filter: true
            },
            {
                Field: 'telefone',
                DisplayName: 'Telefone',
                CellTemplate: undefined,
                Width: 2,
                ActionButton: undefined,
                Type: ETypeFilter.String,
                ServerField: 'Telefone',
                Filter: true
            },
            {
                Field: 'celular',
                DisplayName: 'Celular',
                CellTemplate: undefined,
                Width: 2,
                ActionButton: undefined,
                Type: ETypeFilter.String,
                ServerField: 'celular',
                Filter: true
            },
            {
                Field: 'ativo',
                DisplayName: 'Status',
                Width: 2,
                CellTemplate: undefined,
                ActionButton: undefined,
                Type: ETypeFilter.String,
                ServerField: 'ativo',
                Filter: false
            },
            {
                Field: 'dataCadastro',
                DisplayName: 'Data Cadastro',
                Width: 2,
                CellTemplate: undefined,
                ActionButton: undefined,
                Type: ETypeFilter.String,
                ServerField: 'dataCadastro',
                Filter: false
            },
            {
                Field: 'dataAlteracao',
                DisplayName: 'Data Alteração',
                Width: 2,
                CellTemplate: undefined,
                ActionButton: undefined,
                Type: ETypeFilter.String,
                ServerField: 'dataAlteracao',
                Filter: false
            }
        ]
    }
}
