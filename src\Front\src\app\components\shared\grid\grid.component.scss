:host {
    height: 100%;
    .div-flex {
        display: flex;
        flex-flow: column;
        height: 100%;
        width: 100%;

        .mat-sidenav-content {
            display: flex;
            flex-direction: column;
            align-content: flex-start;
            justify-content: flex-start;
            height: 100%;
            overflow-y: hidden;
            background: white;

            .table-container {
                width: 100vw; 
            }

            .mat-table {
                display: block;
                width: 100%;
                overflow-x: auto;
            }
        }
    }
}

@for $index from 0 through 200 {
    $size: $index * 2;
    .table-column-#{$size} {
        min-width: #{$size}#{"px"};
    }
}

.mat-header-cell, .mat-cell {
    padding: 4px; // Reduzindo o padding das células
    height: 15px; // Definindo uma altura menor para as células
}

.mat-row, .mat-header-row {
    height: 24px; // Definindo uma altura menor para as linhas
}


.dot {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
    vertical-align: middle;
}

.icon-circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 25px;
    height: 25px;
    background-color: #f0f0f0;
    border-radius: 50%; 
    font-size: 16px;
    color: #495057;
    margin-right: 8px;
    border: 1px solid #e6e6e6;
}

  .base64-image {
    display: inline-block;
    width: 25px; 
    height: 25px;
    border-radius: 50%; 
    margin-right: 8px; 
    vertical-align: middle;
    object-fit: cover; 
    border: 1px solid #e6e6e6;
}

.botao-container {
    display: flex !important;
    flex-direction: row !important;
    align-items: center;
    gap: 8px; /* Espaçamento entre os botões */
  }