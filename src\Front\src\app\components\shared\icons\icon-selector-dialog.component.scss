/* icon-selector-dialog.component.scss */
.custom-dialog {
  width: 300px; /* Largura fixa da modal */
  height: 400px; /* Altura fixa da modal */
  max-width: 90vw; /* Máximo de 90% da largura da viewport */
  max-height: 80vh; /* Máximo de 80% da altura da viewport */
  overflow: hidden; /* Ocultar overflow extra */
}

.mat-dialog-content {
  display: flex;
  flex-direction: column;
  max-height: 100%; /* Garantir que o conteúdo se ajuste à altura da modal */
}

.input-container {
  position: relative;
  margin-bottom: 1rem; /* Espaçamento inferior */
}

.input-container label {
  display: block;
  margin-bottom: 0.5rem; /* Espaçamento entre o label e o campo de texto */
  font-size: 1rem;
  font-weight: bold;
}

.input-container input {
  width: 100%;
  height: 3rem; /* Aumenta a altura do campo de texto */
  padding: 0.5rem 2.5rem 0.5rem 2.5rem; /* Espaçamento interno ajustado para o ícone */
  font-size: 1.125rem; /* Ajusta o tamanho da fonte */
  border: 1px solid #ccc;
  border-radius: 4px; /* Adiciona borda arredondada ao campo de texto */
  box-sizing: border-box; /* Inclui padding e border no cálculo da largura */
}

.input-container .icon {
  position: absolute;
  top: 50%;
  left: 0.5rem; /* Ajuste conforme necessário */
  transform: translateY(-50%);
  font-size: 1.5rem; /* Ajusta o tamanho do ícone */
  color: #aaa;
}

.icon-list-container {
  flex: 1; /* Faz com que a lista de ícones ocupe o espaço restante */
  overflow: auto; /* Adiciona rolagem interna se necessário */
}

.icon-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 10px;
}

.icon-list button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  font-size: 24px;
  padding: 0;
}

.icon-list i {
  font-size: 24px;
}
