<div class="app-menu navbar-menu">
    <!-- LOGO -->
    <div class="navbar-brand-box">
        <!-- Dark Logo-->
        <a routerLink="/" class="logo logo-dark">
            <span class="logo-sm">
                <img src="{{logoSm}}" alt="" height="22">
            </span>
            <span class="logo-lg">
                <img src="{{logoEstendida}}" alt="" height="17">
            </span>
        </a>
        <!-- Light Logo-->
        <a routerLink="/" class="logo logo-light">
            <span class="logo-sm">
                <img src="{{logoSm}}" alt="" height="22">
            </span>
            <span class="logo-lg">
                <img src="{{logoEstendida}}" alt="" height="17">
            </span>
        </a>
        <button type="button" class="btn btn-sm p-0 fs-20 header-item float-end btn-vertical-sm-hover"
                id="vertical-hover">
            <i class="ri-record-circle-line"></i>
        </button>
    </div>

    <div id="scrollbar">
        <div class="container-fluid">

            <div id="two-column-menu">
            </div>
            <ul class="navbar-nav" id="navbar-nav">
                <ng-container *ngFor="let menuModulo of menuModulos">
                    <li class="nav-item">
                        <!--Modulos-->
                        <a (click)="toggleItem($event)" href="javascript:void(0);"
                           class="is-parent nav-link menu-link has-dropdown"
                           data-bs-toggle="collapse" aria-expanded="false">
                            <i class="{{ menuModulo.icone }} icon nav-icon"></i>
                            <span class=""> {{ menuModulo.descricao }}</span>
                            <span class="fa arrow"></span>
                        </a>
                        <!--Filhos de um menuModulo (menuPais)-->
                        <div class="collapse menu-dropdown" id="sidebarDashboards">
                            <ul class="nav nav-sm flex-column" aria-expanded="false">
                                <li *ngFor="let menuPai of menuModulo.menuPais" class="nav-item">
                                    <a class="nav-link" href="javascript:void(0);"
                                       [attr.data-parent]="menuPai.idMenuPai" (click)="updateActive($event)">
                                        <span class="">{{ menuPai.descricao }}</span>
                                        <i class="bx icon mdi mdi-chevron-right ms-auto nav-icon"></i>
                                    </a>
                                    <!--Filhos de um menuPai (menus com links)-->
                                    <div class="collapse menu-dropdown">
                                        <ul class="nav nav-sm flex-column" aria-expanded="false">
                                            <li *ngFor="let menuFilho of menuPai.menuFilhos" class="nav-item">
                                                <a [attr.data-parent]="menuFilho.idMenuPai"
                                                   routerLink="/{{menuFilho.link}}"
                                                   class="nav-link" (click)="updateActive($event)">
                                                    {{ menuFilho.descricao }}
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </li>
                </ng-container>
            </ul>
        </div>
        <!-- Sidebar -->
    </div>
</div>