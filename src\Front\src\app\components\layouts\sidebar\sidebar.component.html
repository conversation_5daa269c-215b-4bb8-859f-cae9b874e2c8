<!-- ========== App Menu ========== -->
<div class="app-menu navbar-menu">
    <!-- LOGO -->
    <div class="navbar-brand-box">
        <!-- Dark Logo c/ link p home-->
        <a routerLink="/" class="logo logo-dark">
            <span class="logo-sm">
                <img src="{{logoSm}}" alt="" height="42">
            </span>
            <span class="logo-lg">
                <img src="{{logoEstendida}}" alt="" height="36">
            </span>
        </a>
        <!-- Light Logo c/ link p home-->
        <a routerLink="/" class="logo logo-light">
            <span class="logo-sm">
                <img src="{{logoSm}}" alt="" height="42">
            </span>
            <span class="logo-lg">
                <img src="{{logoEstendida}}" alt="" height="36">
            </span>
        </a>
        <button type="button" class="btn btn-sm p-0 fs-20 header-item float-end btn-vertical-sm-hover"
                id="vertical-hover" (click)="toggleMobileMenu($event)">
            <i class="ri-record-circle-line"></i>
        </button>
    </div>

    <div id="scrollbar">
        <div class="container-fluid">
            <div id="two-column-menu"></div>
            <ngx-simplebar class="sidebar-menu-scroll" style="max-height: calc(100vh - 70px);">
                <ul class="metismenu list-unstyled navbar-nav" id="navbar-nav">
                    <li class="menu-title">
                        <i class="fa fa-home"></i>
                        <span data-key="t-menu">Principal</span>
                    </li>
                    <!--Pega os menuModulos do controller-->
                    <!-- Modulo -->
                    <ng-container *ngFor="let menuModulo of menuModulos">
                        <li class="nav-item">
                            <a [routerLink]="!menuModulo.subMenus ? '/' + menuModulo.link : null"
                                routerLinkActive="active"
                                (click)="toggleMenuModulo($event)"
                                class="is-parent nav-link menu-link has-dropdown"
                                [attr.data-bs-toggle]="menuModulo.subMenus ? 'collapse' : null"
                                [attr.aria-expanded]="menuModulo.subMenus ? 'false' : null">
                                <i class="{{ menuModulo.icone }} icon nav-icon"></i>
                                <span>{{ menuModulo.descricao }}</span>
                                <span *ngIf="menuModulo.subMenus" class="fa arrow"></span>
                            </a>

                            <!-- Filhos de um menuModulo (menuPais) -->
                            <div *ngIf="menuModulo.subMenus" class="collapse menu-dropdown">
                                <ul class="nav nav-sm flex-column" aria-expanded="false">
                                    <li *ngFor="let menuPai of menuModulo.menuPais" class="nav-item">
                                        <a class="nav-link"
                                            routerLinkActive="active"
                                            [routerLink]="!menuPai.menuFilhos || menuPai.menuFilhos.length === 0 ? '/' + menuPai.link : null"
                                            [attr.href]="!menuPai.link || (menuPai.menuFilhos && menuPai.menuFilhos.length > 0) ? 'javascript:void(0);' : null"
                                            [attr.data-parent]="menuPai.idMenuPai"
                                            id="menuPai{{menuPai.idMenuPai}}"
                                            [attr.data-bs-toggle]="menuPai.menuFilhos && menuPai.menuFilhos.length > 0 ? 'collapse' : null"
                                            aria-expanded="false"
                                            (click)="handleMenuClick($event, menuPai)"
                                            [ngClass]="{'hide-arrow': !menuPai.menuFilhos || menuPai.menuFilhos.length === 0 && menuPai.link}">
                                            {{ menuPai.descricao }}
                                            <span class="fa arrow"></span>
                                        </a>
                                        <!-- Filhos de um menuPai (menus com links) -->
                                        <div class="collapse menu-dropdown sub-menu">
                                            <ul class="nav nav-sm flex-column" aria-expanded="false">
                                                <li *ngFor="let menuFilho of menuPai.menuFilhos" class="nav-item">
                                                    <a [attr.data-parent]="menuFilho.idMenuPai"
                                                       routerLinkActive="active"
                                                       routerLink="/{{menuFilho.link}}"
                                                       class="nav-link"
                                                       (click)="updateActive($event)">
                                                        {{ menuFilho.descricao }}
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                        <!-- End Filhos de um menuPai (menus com links) -->
                                    </li>
                                </ul>
                            </div>
                            <!-- End Filhos de um menuModulo (menuPais) -->
                        </li>
                    </ng-container>
                </ul>
            </ngx-simplebar>
        </div>
        <!-- Sidebar -->
    </div>
    <div class="sidebar-background"></div>
</div>
<!-- Left Sidebar End -->
<!-- Vertical Overlay-->
<div class="vertical-overlay" (click)="SidebarHide()"></div>
