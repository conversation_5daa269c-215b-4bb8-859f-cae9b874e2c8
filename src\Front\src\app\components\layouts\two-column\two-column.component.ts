import { Component, OnInit } from '@angular/core';


// pai do app-two-column-sidebar
@Component({
    selector: 'app-two-column',
    templateUrl: './two-column.component.html',
    styleUrls: ['./two-column.component.scss']
})

/**
 * TwoColumnComponent
 */
export class TwoColumnComponent implements OnInit {
    menuModuloIconeAtivo?: any;
    constructor() {
    }

    ngOnInit(): void {
    }

    /**
     * Ativa e desativa o menu mobile do two-column e tambem o icone do modulo 
     * com base no menu estar aberto ou nao.
     * Caso nao ter nenhum modulo aberto ao abrir o menu, ativa o primeiro modulo.
     */
    onToggleMobileMenu() {
        if(document.body.classList.contains('twocolumn-panel')){
            document.body.classList.remove('twocolumn-panel');
        } else {
            document.body.classList.add('twocolumn-panel');
        }
        
        const ulTwoColumnSidebarNav = document.getElementById("two-column-menu");
        if (ulTwoColumnSidebarNav) {
            // lista de icones de menuModulos (o primeiro da lista é a logo da empresa)
            const menuModuloIcones = Array.from(ulTwoColumnSidebarNav.getElementsByTagName("a"));
            // atribui ao menu modulo ativo atual um novo caso tenha sido clicado, ou continua o mesmo
            this.menuModuloIconeAtivo = menuModuloIcones.find((x: any) => x.classList.contains("active")) ?? this.menuModuloIconeAtivo;

            // caso o menu seja aberto e n tenha nenhum selecionado
            if (!this.menuModuloIconeAtivo) {
                // pega o primeiro modulo da lista
                this.menuModuloIconeAtivo = menuModuloIcones[1];
                // pega os pais desse cara
                let divMenuPaisDoModulo = document.getElementById('menuPaiLista'+this.menuModuloIconeAtivo.getAttribute('idMenuModulo')) as any;
                // ativa os pais dele
                divMenuPaisDoModulo.classList.add('show')
            }
        }
        
        // ativa o modulo
        this.menuModuloIconeAtivo.classList.toggle("active");
    }

    /**
     * on settings button clicked from topbar
     */
    onSettingsButtonClicked() {
        document.body.classList.toggle('right-bar-enabled');
        const rightBar = document.getElementById('theme-settings-offcanvas');
        if (rightBar != null) {
            rightBar.classList.toggle('show');
            rightBar.setAttribute('style', "visibility: visible;");
        }
    }

}
