import { Component, OnInit } from '@angular/core';
import { TemaService } from "../../../services/tema/tema.service";

@Component({
    selector: 'app-vertical',
    templateUrl: './vertical.component.html',
    styleUrls: ['./vertical.component.scss']
})
export class VerticalComponent implements OnInit {

    isCondensed = false;
    isMediumSize = false;

    constructor(private temaService: TemaService) {
    }

    ngOnInit(): void {
        window.addEventListener('resize', function () {
            if (document.documentElement.clientWidth <= 767) {
                document.documentElement.setAttribute('data-sidebar-size', '');
            } else if (document.documentElement.clientWidth <= 1024) {
                document.documentElement.setAttribute('data-sidebar-size', 'sm');
            } else if (document.documentElement.clientWidth >= 1024) {
                document.documentElement.setAttribute('data-sidebar-size', 'lg');
            }
        })
    }

    /**
     * On mobile toggle button clicked
     */
    onToggleMobileMenu() {
        document.body.classList.toggle('sidebar-enable');
        const currentSidebarSize = document.documentElement.getAttribute("data-sidebar-size");
        if (document.documentElement.clientWidth >= 767) {
            if (currentSidebarSize == null) {
                (document.documentElement.getAttribute('data-sidebar-size') == null || document.documentElement.getAttribute('data-sidebar-size') == "lg") ? document.documentElement.setAttribute('data-sidebar-size', 'sm') : document.documentElement.setAttribute('data-sidebar-size', 'lg')
            } else if (currentSidebarSize == "md") {
                this.isMediumSize = true;
                (document.documentElement.getAttribute('data-sidebar-size') == "md") ? document.documentElement.setAttribute('data-sidebar-size', 'sm') : document.documentElement.setAttribute('data-sidebar-size', 'md')
            } else {
                // menu tava pequeno qnd clicou?
                (document.documentElement.getAttribute('data-sidebar-size') == "sm") ? 
                    // se sim, antes ele era medio?
                    // se sim, volta pro medio, se nao, volta pro largo
                    (this.isMediumSize ? document.documentElement.setAttribute('data-sidebar-size', 'md') : document.documentElement.setAttribute('data-sidebar-size', 'lg'))
                    // se ele nao tava pequeno, vai pra pequeno
                    : document.documentElement.setAttribute('data-sidebar-size', 'sm')
                this.isMediumSize = false;
            }
        }
        // if (document.documentElement.clientWidth <= 767) {
        if (document.documentElement.clientWidth <= 767) {
            document.body.classList.toggle('vertical-sidebar-enable');
        }
        this.isCondensed = !this.isCondensed;
    }

    /**
     * on settings button clicked from topbar
     */
    onSettingsButtonClicked() {
        document.body.classList.toggle('right-bar-enabled');
        const rightBar = document.getElementById('theme-settings-offcanvas');
        if (rightBar != null) {
            rightBar.classList.toggle('show');
            rightBar.setAttribute('style', "visibility: visible;");

        }
    }

}
