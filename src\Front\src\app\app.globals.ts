interface AppGlobals {
    TOKEN: any
    HOME: string
    API_URL: string
    LOGO_ESTENDIDA: string
    LOGO_SM: string
    IMG_LOGIN: string
    IMG_COMPANY: string
}

export let AppGlobals : AppGlobals = {
    //Token atual
    TOKEN: {'Authorization': `Bearer ${localStorage.getItem('SessionKey')}`},
    //Rota da pagina principal
    HOME: "dashboard",
    //Local
    API_URL: "http://localhost:51002/",
    //API_URL: "http://***************:9019/Web/",
    //Dev
    // API_URL: "http://sw19-142:9019/Web",
    //Prod
    //API_URL: "http://***************:8006/",
    //Logo grande escrito sistemainfo
    LOGO_ESTENDIDA: "assets/logos/sistema-info-grande-transparente.png",
    //Logo pequena apenas icone da empresa
    LOGO_SM: "assets/logos/sistema-info-logo-sm-transparente.png",
    IMG_LOGIN: "assets/images/img_si.jpg",
    IMG_COMPANY: "assets/images/ic_sistema_info.png"
    
}