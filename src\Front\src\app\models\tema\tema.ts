import { BaseResponse } from "../base/base-response"

export interface UsuarioTemaBaseResponse extends BaseResponse
{
    data: UsuarioTemaResponse
}

export interface UsuarioTemaSalvarResponse extends BaseResponse
{
    data: number // ID do tema criado/atualizado
}

//do backend pra cá
export interface UsuarioTemaResponse
{
    template: string
    layoutColor: string
    layoutStyle: string
    layoutWidth: string
    layoutPosition: string
    topbarColor: string
    sidebarSize: string
    sidebarView: string
    sidebarColor: string
}

//daqui pro backend
export interface UsuarioTemaRequest extends UsuarioTemaResponse
{
    id?: number
    usuarioId?: number
}