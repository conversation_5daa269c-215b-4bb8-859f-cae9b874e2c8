@media (min-width: 1200px) {
    .container, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl {
        max-width: 100%;
    }
}

.card {
    border-radius: 12px;
}

.card-header:first-child {
    border-radius: 12px 12px 0 0;
}

/* Estilo opcional para alinhamento e margens */
.d-flex {
    display: flex;
}

.align-items-center {
    align-items: center;
}

.me-2 {
    margin-right: 0.5rem;
}

.ms-2 {
    margin-left: 0.5rem;
}

.btn-outline-secondary:hover {
    color: #fff;
    background-color: #41b006;
    border-color: #41b006;
}

.dropdown.w-100 .dropdown-menu {
    width: 100% !important;
}

.btn.dropdown-toggle .caret {
    margin-left: auto;
    margin-right: 0;
}

.btn-outline-secondary .caret {
    border-top: 0.3em solid;
    border-right: 0.3em solid transparent;
    border-left: 0.3em solid transparent;
}

.btn.dropdown-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.btn-outline-secondary {
    color: #41b006;
    border-color: #41b006;
}

.circle {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.ng-autocomplete {
    width: 100%;
}

.profile-user-wrapper {
    position: absolute;
    top: 0;
    right: 0;
    margin: 10px;
}

.profile-user img {
    width: 30px;
    height: 30px;
    border: 1px solid white; /* Ajuste a largura da borda */
    box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.1); /* Suaviza o contorno */
}

.profile-photo-edit {
    position: absolute;
    bottom: -5px;
    right: -5px;
    width: 18px; /* Reduz o tamanho do círculo de edição */
    height: 18px; /* Reduz o tamanho do círculo de edição */
}

.profile-photo-edit .avatar-title {
    font-size: 10px; /* Ajusta o tamanho do ícone dentro do círculo */
    padding: 2px;
}

.img-thumbnail {
    padding: 0rem;
}

.mat-calendar-body-selected {
    background-color: #8f0404;
    color: #fff;
}

.chip-content {
    display: flex;
    align-items: center;
    gap: 8px; /* Espaço entre texto e ícone */
}
    
.chip-icon {
    font-size: 16px; /* Ajusta o tamanho do ícone */
    display: flex;
    align-items: center;
    justify-content: center;
}

.chip-content {
    display: flex;
    align-items: center;
}
  
.chip-text {
    align-items: center;
    justify-content: center;
   
}
  
.chip-add {
    cursor: pointer;
    font-weight: bold;
    display: flex;
    align-items: center;
}

