.no-submenu-link {
    color: red; /* ou a cor desejada */
}

.no-submenu-link .icon {
    color: var(--vz-vertical-menu-item-active-color); /* ou a cor desejada */
}

.navbar-menu .navbar-nav .nav-link.active {
    // color: #d5060e;
    color: var(--vz-vertical-menu-item-active-color);
}

.fa.arrow {
    display: none;
}

a.nav-link > .fa.arrow {
    display: inline; /* ou flex, conforme necessário */
}

.hide-arrow .fa.arrow {
    display: none; /* Esconde a flecha quando a classe hide-arrow está presente */
}


.vertical-overlay {
    display: block; /* Exibe o elemento por padrão */
}


.vertical-overlay {
    display: none; /* Oculta o elemento para telas maiores que 500px */
}
