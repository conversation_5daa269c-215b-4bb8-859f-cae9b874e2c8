import { Injectable } from "@angular/core";
import { ActionRow } from "src/app/models/grid/ActionRow";
import { EventService } from "../event/event.service";


@Injectable({
    providedIn: 'root'
})

export class GridService{

    // Controle de debounce para evitar múltiplas chamadas
    private reloadTimeouts: Map<string, any> = new Map();

    constructor(private eventService: EventService) {
    }

    getActionRowGrid(action: ActionRow){
        this.eventService.broadcast('gridActionRow', action);
    }

    recarregarGrid(gridId?: string) {
        const key = gridId || 'default';

        // Cancelar timeout anterior se existir
        if (this.reloadTimeouts.has(key)) {
            clearTimeout(this.reloadTimeouts.get(key));
            console.log(`Cancelando reload anterior para grid: ${key}`);
        }

        // Criar novo timeout com debounce de 300ms
        const timeoutId = setTimeout(() => {
            console.log(`Executando reload para grid: ${key}`);
            this.eventService.broadcast(`gridRecarregar_${gridId}`);
            this.reloadTimeouts.delete(key);
        }, 300);

        this.reloadTimeouts.set(key, timeoutId);
        console.log(`Agendando reload para grid: ${key}`);
    }

    dadosCarregados(dados: any[]) {
        this.eventService.broadcast('gridDadosCarregados', dados);
    }

    totalItensGrid(total: Number) {
        this.eventService.broadcast('gridTotalDadosCarregados', total);
    }

    totalItensDialog(total: Number) {
        this.eventService.broadcast('gridDialogTotalDadosCarregados', total);
    }
}

