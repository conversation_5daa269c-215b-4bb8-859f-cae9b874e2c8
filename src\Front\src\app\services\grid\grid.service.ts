import { Injectable } from "@angular/core";
import { ActionRow } from "src/app/models/grid/ActionRow";
import { EventService } from "../event/event.service";


@Injectable({
    providedIn: 'root'
})

export class GridService{
    
    constructor(private eventService: EventService) {
    }
    
    getActionRowGrid(action: ActionRow){
        this.eventService.broadcast('gridActionRow', action);
    }

    recarregarGrid(gridId?: string) {
        // Se gridId for fornecido, use um evento específico para essa grid
      
            console.log(`Recarregando grid específica: ${gridId}`);
            this.eventService.broadcast(`gridRecarregar_${gridId}`);
        
    }

    dadosCarregados(dados: any[]) {
        this.eventService.broadcast('gridDadosCarregados', dados);
    }

    totalItensGrid(total: Number) {
        this.eventService.broadcast('gridTotalDadosCarregados', total);
    }

    totalItensDialog(total: Number) {
        this.eventService.broadcast('gridDialogTotalDadosCarregados', total);
    }
}

