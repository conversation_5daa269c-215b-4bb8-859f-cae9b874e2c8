<!-- ========== App Menu ========== -->
<div class="app-menu navbar-menu">
    <!-- LOGO -->
    <div class="navbar-brand-box">
        <!-- Dark Logo-->
        <a routerLink="/" class="logo logo-dark">
          <span class="logo-sm">
              <img src="{{logoSm}}" alt="" height="42">
          </span>
            <span class="logo-lg">
              <img src="{{logoEstendida}}" alt="" height="30">
          </span>
        </a>
        <!-- Light Logo-->
        <a routerLink="/" class="logo logo-light">
          <span class="logo-sm">
              <img src="{{logoSm}}" alt="" height="42">
          </span>
            <span class="logo-lg">
              <img src="{{logoEstendida}}" alt="" height="30">
          </span>
        </a>
        <button type="button" class="btn btn-sm p-0 fs-20 header-item float-end btn-vertical-sm-hover"
                id="vertical-hover">
            <i class="ri-record-circle-line"></i>
        </button>
    </div>

    <div id="scrollbar">
        <div class="container-fluid">
            <div id="two-column-menu">
                <ul class="twocolumn-iconview" data-simplebar="init">
                    <div class="simplebar-wrapper" style="margin: 0px;">
                        <div class="simplebar-mask">
                            <div class="simplebar-offset" style="right: 0px; bottom: 0px;">
                                <!--BARRA DOS ICONES-->
                                <ngx-simplebar class="simplebar-content-wrapper" style="max-height: 100vh">
                                    <div class="simplebar-content" style="padding: 0px;">
                                        <a routerLink="/" class="logo">
                                            <img src="{{logoSm}}" alt="" height="42">
                                        </a>
                                        <!--ICONES DOS MODULOS-->
                                        <ng-container *ngFor="let menuModulo of menuModulos">
                                            <li>
                                                <a href="javascript:void(0);"
                                                   id="menuModulo{{menuModulo.idMenu}}"
                                                   attr.idMenuModulo="{{menuModulo.idMenu}}"
                                                   class="nav-icon"
                                                   (click)="toggleMenuModulo($event)">
                                                    <i class="{{ menuModulo.icone }}"></i>
                                                    <span data-key="t-dashboards" class="d-none">{{menuModulo.descricao}}</span>
                                                </a>
                                            </li>
                                        </ng-container>
                                    </div>
                                </ngx-simplebar>
                            </div>
                        </div>
                    </div>
                </ul>
            </div>

            <ul class="navbar-nav" id="navbar-nav" data-simplebar="init">
                <div class="simplebar-wrapper" style="margin: 0px;">
                    <div class="simplebar-height-auto-observer-wrapper">
                        <div class="simplebar-height-auto-observer"></div>
                    </div>
                    <div class="simplebar-mask">
                        <div class="simplebar-offset" style="right: 0px; bottom: 0px;">
                            <!--BARRA DOS ITENS-->
                            <ngx-simplebar class="simplebar-content-wrapper" style="max-height: 100vh">
                                <div class="simplebar-content" style="padding: 0px;">
                                    <!--Titulo-->
                                    <li class="menu-title">
                                        <span data-key="t-menu">
                                            Principal
                                        </span>
                                    </li>
                                    <!--Modulos-->
                                    <li class="nav-item" *ngFor="let menuModulo of menuModulos">
                                        <ng-container>
                                            <div class="collapse menu-dropdown" 
                                                 id="menuPaiLista{{menuModulo.idMenu}}"
                                                 attr.idMenuModulo="{{menuModulo.idMenu}}"
                                                 [attr.data-name]="menuModulo.descricao" 
                                                 aria-expanded="true">
                                                <ul class="nav nav-sm flex-column"
                                                    aria-expanded="false">
                                                    <!--Filhos de um menuModulo (menuPais)-->
                                                    <li *ngFor="let menuPai of menuModulo.menuPais" class="nav-item">
                                                        <a href="javascript:void(0);"
                                                           class="nav-link ecomm" 
                                                           id="menuPai{{menuPai.idMenu}}"
                                                           data-bs-toggle="collapse"
                                                           [attr.aria-expanded]="false"
                                                           [attr.data-parent]="menuPai.idMenuPai"
                                                           (click)="toggleMenuPai($event)">
                                                            {{ menuPai.descricao}}
                                                        </a>
                                                        <!--Filhos de um menuPai (menus com links)-->
                                                        <div class="collapse menu-dropdown sub-menu"
                                                             attr.idMenuModulo="{{menuModulo.idMenu}}">
                                                            <ul class="nav nav-sm flex-column">
                                                                <li *ngFor="let menuFilho of menuPai.menuFilhos"
                                                                    class="nav-item">
                                                                    <a [attr.data-parent]="menuFilho.idMenuPai"
                                                                       routerLink="/{{menuFilho.link}}"
                                                                       id="menuFilho{{menuFilho.idMenu}}"
                                                                       class="nav-link" 
                                                                       (click)="updateActive($event)">
                                                                        {{ menuFilho.descricao }}
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </div>
                                        </ng-container>
                                    </li> <!-- end Dashboard Menu -->
                                </div>
                            </ngx-simplebar>
                        </div>
                    </div>
                    <div class="simplebar-placeholder" style="width: auto; height: 183px;"></div>
                </div>
            </ul>
        </div>
        <!-- Sidebar -->
    </div>
    <div class="sidebar-background"></div>
</div>
<!-- Left Sidebar End -->
<!-- Vertical Overlay-->
<div class="vertical-overlay"></div>
