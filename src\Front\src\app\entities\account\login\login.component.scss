/* Estilos gerais */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
}

.auth-page-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.auth-one-bg-position, .auth-one-bg, #auth-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: -1;
}

.auth-page-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: var(--vz-card-bg);
}

.container-fluid {
    display: flex;
    width: 100%;
    height: 100%;
    padding: 0;
}

.image-section, .form-section {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 0;
}

.image-section {
    background-color: #007bff;
    flex: 1;
}

.image-section img {
    max-width: 100%;
    height: auto;
}

.form-section {
    background-color: #ffffff;
    flex: 1;
}

.card {
    width: 100%;
    max-width: 400px;
}

.full-screen-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.full-screen-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.2);
}

.overlay-content {
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    min-width: 450px;
    background-color: rgba(255, 255, 255);
}

.card-login {
    width: 100%;
    max-width: 400px;
    margin-bottom: 1.5rem;
    position: relative;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: var(--vz-card-bg);
    background-clip: border-box;
    border: 0 solid rgba(0, 0, 0, 0.125);
    border-radius: 0.25rem;
}

.color-text-primary {
    color: #525252;
}

.bottom-left-image {
    position: absolute;
    bottom: 20px;
    left: 20px;
    z-index: 1;
}

.bottom-left-image img {
    width: 290px;
    height: auto;
    transition: width 0.5s ease-in-out;
}

.top-right-image img {
    width: auto;
    height: 60px; 
    transition: height 0.5s ease-in-out;
}

.size-text-title {
    font-size: 25px;
    transition: font-size 0.5s ease-in-out;
}

.size-text-sub-title {
    font-size: 15px;
    transition: font-size 0.5s ease-in-out;
}

@media (max-width: 902px) {
    .bottom-left-image img {
        width: 260px;
        height: auto;
    }

    .top-right-image img {
        width: auto;
        height: 55px;
        transition: height 0.5s ease-in-out;
    }

    .size-text-title {
        font-size: 22px;
        transition: font-size 0.5s ease-in-out;
    }
    

    .size-text-sub-title {
        font-size: 12px;
        transition: font-size 0.5s ease-in-out; 
    }
}

@media (max-width: 767px) {
    .full-screen-image,
    .overlay-content {
        width: 100%;
        height: auto;
        min-width: 0;
        height: 100%;
        position: relative;
     
    }

    .bottom-left-image {
        display: none; 
        transition: opacity 0.5s ease-in-out, visibility 0.5s ease-in-out;
    }

    .top-right-image img {
        width: auto;
        height: 50px;  
        transition: height 0.5s ease-in-out; 
    }

    .size-text-title {
        font-size: 20px;
        transition: font-size 0.5s ease-in-out; 
    }

    .size-text-sub-title {
        font-size: 12px; 
        transition: font-size 0.5s ease-in-out;
    }
}

.btn-success {
    color: #fff;
    background-color: #DB112B;
    border-color: #DB112B;
}

.btn-success:hover {
    color: #fff;
    background-color: #920c1e;
    border-color: #920c1e;
}

.text-decoration-login {
    color: #DB112B;
    text-decoration: underline;
}

@media (min-width: 768px) {
    .overlay-content.full-screen {
        width: 100%;
    }
}
