import { Component, OnInit, EventEmitter, Output, Inject } from '@angular/core';
import { DOCUMENT } from '@angular/common';

//Logout
import { Router } from '@angular/router';

// Language
import { CookieService } from 'ngx-cookie-service';
import { AppGlobals } from "../../../app.globals";
import { EventService } from "../../../services/event/event.service";
import { TemaService } from "../../../services/tema/tema.service";
import { UsuarioLoginResponse } from "../../../models/usuario/usuario-login";
import { ToastrService } from "ngx-toastr";

@Component({
    selector: 'app-topbar',
    templateUrl: './topbar.component.html',
    styleUrls: ['./topbar.component.scss']
})
export class TopbarComponent implements OnInit {
    
    @Output() mobileMenuButtonClicked = new EventEmitter();

    element: any;
    logoEstendida = AppGlobals.LOGO_ESTENDIDA;
    logoSm = AppGlobals.LOGO_SM;

    userImageBase64: string | null = null;

    usuario = {
        nome: localStorage.getItem("Nome"),
        administrador: localStorage.getItem("Administrador"),
        descricao: localStorage.getItem("DescicaoGrupo")!.replace(/"/g, ''),
        corGrupo: localStorage.getItem("CorIdentificacaoGrupo")!.replace(/"/g, '')
    }
    
    constructor(@Inject(DOCUMENT) private document: any, 
                private eventService: EventService,
                public _cookiesService: CookieService, 
                private router: Router,
                private temaService: TemaService,
                private toastr: ToastrService) {
    }

    ngOnInit(): void {
        this.element = document.documentElement;
        this.usuario = {
            nome: localStorage.getItem("Nome"),
            administrador: localStorage.getItem("Administrador"),
            descricao: localStorage.getItem("DescicaoGrupo")!.replace(/"/g, ''),
            corGrupo: localStorage.getItem("CorIdentificacaoGrupo")!.replace(/"/g, '')
        }

        this.loadPhotoProfile()
    }

    getTextColor(backgroundColor: string): string {
        // Remove o caractere '#' se estiver presente
        if (backgroundColor.charAt(0) === '#') {
          backgroundColor = backgroundColor.slice(1);
        }
    
        // Converte a cor para RGB
        const r = parseInt(backgroundColor.substr(0, 2), 16);
        const g = parseInt(backgroundColor.substr(2, 2), 16);
        const b = parseInt(backgroundColor.substr(4, 2), 16);
    
        // Calcula o brilho percebido (perceived brightness)
        const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    
        return brightness > 140 ? '#FFFFFF'  : '#333333';
      }

    loadPhotoProfile () {
        var fotoUser = localStorage.getItem("Foto")
        if (fotoUser == null || fotoUser.trim() === "") {
            this.userImageBase64 = "assets/images/users/avatar-1.jpg";
        } else {
            // Remover aspas duplas extras de fotoUserre
            const fotoUserreSemAspas = fotoUser.replace(/"/g, '');
        
            this.userImageBase64 = fotoUserreSemAspas;
        }
    }

    /**
     * Toggle the menu bar when having mobile screen
     */
    toggleMobileMenu(event: any) {
        event.preventDefault();
        this.mobileMenuButtonClicked.emit();
    }

    /**
     * Fullscreen method
     */
    fullscreen() {
        document.body.classList.toggle('fullscreen-enable');
        if (
            !document.fullscreenElement && !this.element.mozFullScreenElement &&
            !this.element.webkitFullscreenElement) {
            if (this.element.requestFullscreen) {
                this.element.requestFullscreen();
            } else if (this.element.mozRequestFullScreen) {
                /* Firefox */
                this.element.mozRequestFullScreen();
            } else if (this.element.webkitRequestFullscreen) {
                /* Chrome, Safari and Opera */
                this.element.webkitRequestFullscreen();
            } else if (this.element.msRequestFullscreen) {
                /* IE/Edge */
                this.element.msRequestFullscreen();
            }
        } else {
            if (this.document.exitFullscreen) {
                this.document.exitFullscreen();
            } else if (this.document.mozCancelFullScreen) {
                /* Firefox */
                this.document.mozCancelFullScreen();
            } else if (this.document.webkitExitFullscreen) {
                /* Chrome, Safari and Opera */
                this.document.webkitExitFullscreen();
            } else if (this.document.msExitFullscreen) {
                /* IE/Edge */
                this.document.msExitFullscreen();
            }
        }
    }

    /**
     * Topbar Light-Dark Mode Change
     */
    changeLayoutColor(layoutColor: string) {
        this.temaService.changeLayoutColor(layoutColor);
    }

    /**
     * Logout the user
     */
    logout() {
        localStorage.clear();
        this.router.navigate(['/auth']);
        this.toastr.success('Logout realizado com sucesso', 'Mensagem')
    }

    windowScroll() {
        if (document.body.scrollTop > 100 || document.documentElement.scrollTop > 100) {
            (document.getElementById("back-to-top") as HTMLElement).style.display = "block";
        } else {
            (document.getElementById("back-to-top") as HTMLElement).style.display = "none";

        }
    }

}
