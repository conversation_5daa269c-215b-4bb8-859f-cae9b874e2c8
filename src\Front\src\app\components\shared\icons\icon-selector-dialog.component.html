

<!-- icon-selector-dialog.component.html -->
<div mat-dialog-content>
  <h2 mat-dialog-title>Se<PERSON>cione um ícone</h2>
  <div class="input-container">
  
    <div class="icon">
      <i class="mdi mdi-magnify"></i> <!-- Ícon<PERSON> de lupa -->
    </div>
    <input 
      id="iconFilter" 
      type="text" 
      [(ngModel)]="filterText" 
      (input)="filterIcons()" 
      placeholder="Digite o nome do ícone"
    />
  </div>

  <div class="icon-list-container">
    <div class="icon-list">
      <button *ngFor="let icon of filteredIcons" mat-button (click)="selectIcon(icon)">
        <i [class]="icon"></i>
      </button>
    </div>
  </div>

</div>
