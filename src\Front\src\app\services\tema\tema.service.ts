import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { UsuarioTemaResponse, UsuarioTemaRequest, UsuarioTemaBaseResponse, UsuarioTemaSalvarResponse } from 'src/app/models/tema/tema';
import { BaseService } from "../base/base.service";
import { EventService } from "../event/event.service";
import { BaseResponse } from "../../models/base/base-response";

@Injectable({
    providedIn: 'root'
})

//Centraliza funções de salvar, carregar e trocar os temas da aplicação
export class TemaService {

    //Objeto que vai ficar sendo atualizado a cada mudança pra depois ser mandado pro back ao salvar
    //Se nao houver temaId entao é um usuario que nao tinha um registro de tema no banco.
    //Apenas ao clicar em salvar será enviado os dados e criado o registro no backend.
    temaAtual!: UsuarioTemaRequest;

    temaDefault: UsuarioTemaResponse = {
        template: 'material',
        layoutColor: 'light',
        layoutStyle: 'vertical',
        layoutWidth: 'fluid',
        layoutPosition: 'fixed',
        topbarColor: 'light',
        sidebarSize: 'lg',
        sidebarView: 'default',
        sidebarColor: 'dark'
    }

    constructor(private baseService: BaseService,
                private eventService: EventService,
                private toastr: ToastrService) {
    }

    //Carrega o tema do backend com base no id do usuário atual
    getTema() {
        let temaId = Number(localStorage.getItem('TemaId'));

        // Verificar se há tema salvo no localStorage primeiro
        const temaLocalString = localStorage.getItem('Tema');

        if (!temaId) {
            // Se não tem TemaId mas tem tema no localStorage, usar o localStorage
            if (temaLocalString) {
                try {
                    const temaLocal = JSON.parse(temaLocalString);
                    console.log('TemaService: Usando tema do localStorage (sem TemaId)', temaLocal);
                    this.setTema(temaLocal);
                    return;
                } catch (e) {
                    console.error('Erro ao parsear tema do localStorage:', e);
                }
            }
            // Se não tem nem TemaId nem localStorage válido, usar padrão
            console.log('TemaService: Usando tema padrão');
            this.setTema(this.temaDefault);
        }
        else {
            // Tem TemaId, buscar no backend
            this.baseService.Get("UsuarioTema", "ConsultarTema", {temaId: temaId}).subscribe(
                (response: UsuarioTemaBaseResponse) => {
                    if (response.success && response.data) {
                        console.log('TemaService: Usando tema do backend', response.data);
                        this.setTema(response.data);
                    } else {
                        // Se falhou no backend, tentar localStorage como fallback
                        if (temaLocalString) {
                            try {
                                const temaLocal = JSON.parse(temaLocalString);
                                console.log('TemaService: Backend falhou, usando localStorage como fallback', temaLocal);
                                this.setTema(temaLocal);
                                return;
                            } catch (e) {
                                console.error('Erro ao parsear tema do localStorage:', e);
                            }
                        }
                        // Se tudo falhou, usar padrão
                        console.log('TemaService: Backend e localStorage falharam, usando tema padrão');
                        this.setTema(this.temaDefault);
                    }
                })
        }
    }

    //Seta na aplicação o tema default ou carrega o do usuario se existir
    setTema(tema: UsuarioTemaResponse) {
        this.temaAtual = tema;
        this.setTemaLocal(tema);
        this.changeTemplate(tema.template);
        this.changeLayoutColor(tema.layoutColor);
        this.changeLayoutPosition(tema.layoutPosition);
        this.changeLayoutStyle(tema.layoutStyle);
        this.changeLayoutWidth(tema.layoutWidth);
        this.changeTopbarColor(tema.topbarColor);
        this.changeSidebarColor(tema.sidebarColor);
        this.changeSidebarSize(tema.sidebarSize);
        this.changeSidebarView(tema.sidebarView);
        document.documentElement.setAttribute('data-sidebar-image', 'none'); //todo imagem talvez
    }

    //Salva o tema atual no localstorage
    setTemaLocal(tema: UsuarioTemaResponse) {
        localStorage.setItem('Tema', JSON.stringify(tema));
    }

    //Envia o objeto de tema atualizado ao backend
    saveTema() {
        let usuarioId = Number(localStorage.getItem('UsuarioId'));
        let temaId = Number(localStorage.getItem('TemaId'));
        localStorage.setItem('TemaSalvando', 'true');

        //se nao tem um tema ja carregado ent é pq o usuario n tem um tema
        //ent faz o id ser 0 pra ser criado no banco para o usuario
        if (isNaN(temaId)) temaId = 0;

        this.temaAtual.usuarioId = usuarioId;
        this.temaAtual.id = temaId;

        this.setTemaLocal(this.temaAtual);

        this.baseService.Post("UsuarioTema", "SalvarTema", this.temaAtual).subscribe(
            (response: BaseResponse) => {
                if (response.success) {
                    this.toastr.success(response.message, 'Mensagem');
                    localStorage.setItem('TemaMudado', 'false');
                } else {
                    this.toastr.error(response.message, 'Mensagem');
                }
                localStorage.setItem('TemaSalvando', 'false');
            }
        );
    }

    //Salva o tema automaticamente no backend (sem mostrar toast)
    saveTemaAuto() {
        let usuarioId = Number(localStorage.getItem('UsuarioId'));
        let temaId = Number(localStorage.getItem('TemaId'));

        // Se não tem usuário logado, não salvar
        if (!usuarioId) {
            console.log('TemaService: Usuário não logado, não salvando tema no backend');
            return;
        }

        // Se não tem um tema já carregado então é porque o usuário não tem um tema
        // então faz o id ser 0 para ser criado no banco para o usuário
        if (isNaN(temaId)) temaId = 0;

        this.temaAtual.usuarioId = usuarioId;
        this.temaAtual.id = temaId;

        this.setTemaLocal(this.temaAtual);

        this.baseService.Post("UsuarioTema", "SalvarTema", this.temaAtual).subscribe(
            (response: UsuarioTemaSalvarResponse) => {
                if (response.success) {
                    console.log('TemaService: Tema salvo automaticamente no backend');
                    // Salvar o TemaId retornado se for um novo tema
                    if (temaId === 0 && response.data) {
                        localStorage.setItem('TemaId', response.data.toString());
                        console.log('TemaService: Novo TemaId salvo:', response.data);
                    }
                } else {
                    console.error('TemaService: Erro ao salvar tema automaticamente:', response.message);
                }
            },
            (error) => {
                console.error('TemaService: Erro na requisição de salvar tema:', error);
            }
        );
    }

    //creative, default, interactive, material, minimal, modern, saas
    changeTemplate(template: string) {
        //id pra poder referenciar mais facilmente depois
        let link = document.getElementById('template-tema') as HTMLLinkElement;

        //caso n tenha encontrado por id por algum motivo
        //ent coloca o id no link injetado pelo angular.json
        if (!link) {
            link = document.querySelector('[href="material.css"]') as HTMLLinkElement;
            link.setAttribute('id', 'template-tema');
        }

        //se está mudando pro template que já está ativo
        if (link?.href == `${template}.css`) {
            return;
        }
        //muda o template
        else {
            link.href = `${template}.css`
            this.temaAtual.template = template;
            this.setTemaLocal(this.temaAtual);
            return;
        }
    }

    //vertical, horizontal, twocolumn
    changeLayoutStyle(layoutStyle: string) {
        //o layout.component trata o broadcast e muda
        this.eventService.broadcast('changeLayoutStyle', layoutStyle);
        document.documentElement.setAttribute('data-layout', layoutStyle);
        this.temaAtual.layoutStyle = layoutStyle;
        this.setTemaLocal(this.temaAtual);
    }

    //light, dark
    changeLayoutColor(layoutColor: string) {
        this.eventService.broadcast('changeLayoutColor', layoutColor);
        document.documentElement.setAttribute('data-layout-mode', layoutColor);
        this.changeSidebarColor(layoutColor);
        this.changeTopbarColor(layoutColor);
        this.temaAtual.layoutColor = layoutColor;
        this.temaAtual.topbarColor = layoutColor;
        this.setTemaLocal(this.temaAtual);

        // Salvar automaticamente no backend quando mudar o tema
        console.log('TemaService: Tema alterado para', layoutColor, '- salvando automaticamente');
        this.saveTemaAuto();
    }

    //light, dark
    changeSidebarColor(sidebarColor: string) {
        document.documentElement.setAttribute('data-sidebar', sidebarColor)
        this.temaAtual.sidebarColor = sidebarColor;
        this.setTemaLocal(this.temaAtual);
    }

    //fixed, scrollable
    changeLayoutPosition(layoutPosition: string) {
        document.documentElement.setAttribute('data-layout-position', layoutPosition)
        this.temaAtual.layoutPosition = layoutPosition;
        this.setTemaLocal(this.temaAtual);
    }

    //light, dark
    changeTopbarColor(topbarColor: string) {
        document.documentElement.setAttribute('data-topbar', topbarColor);
        this.temaAtual.topbarColor = topbarColor;
        this.setTemaLocal(this.temaAtual);
    }

    //lg, md, sm, sm-hover
    changeSidebarSize(sidebarSize: string) {
        document.documentElement.setAttribute('data-sidebar-size', sidebarSize)
        this.temaAtual.sidebarSize = sidebarSize;
        this.setTemaLocal(this.temaAtual);
    }

    //default, detached
    changeSidebarView(sidebarView: string) {
        document.documentElement.setAttribute('data-layout-style', sidebarView)
        this.temaAtual.sidebarView = sidebarView;
        this.setTemaLocal(this.temaAtual);
    }

    //fluid, boxed
    changeLayoutWidth(layoutWidth: string) {
        //o layout.component trata o broadcast e muda
        document.documentElement.setAttribute('data-layout-width', layoutWidth);
        this.temaAtual.layoutWidth = layoutWidth;
        this.setTemaLocal(this.temaAtual);
    }

}