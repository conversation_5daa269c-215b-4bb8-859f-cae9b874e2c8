<!doctype html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <title>Key Two</title>
        <!--<base href="/velzon/angular/material/">-->
        <base href="/">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link rel="icon" type="image/x-icon"
              href="https://s3.amazonaws.com/movidesk-files/3BE38AB04A9092E099FE7AB830509B96">
    </head>
    <body>
        <!-- Script para aplicar tema antes do Angular carregar -->
        <script>
            (function() {
                // Recuperar tema do localStorage
                const temaString = localStorage.getItem('Tema');
                if (temaString) {
                    try {
                        const tema = JSON.parse(temaString);

                        // Aplicar atributos do tema no documentElement
                        if (tema.layoutColor) {
                            document.documentElement.setAttribute('data-layout-mode', tema.layoutColor);
                        }
                        if (tema.layoutStyle) {
                            document.documentElement.setAttribute('data-layout', tema.layoutStyle);
                        }
                        if (tema.layoutPosition) {
                            document.documentElement.setAttribute('data-layout-position', tema.layoutPosition);
                        }
                        if (tema.layoutWidth) {
                            document.documentElement.setAttribute('data-layout-width', tema.layoutWidth);
                        }
                        if (tema.topbarColor) {
                            document.documentElement.setAttribute('data-topbar', tema.topbarColor);
                        }
                        if (tema.sidebarColor) {
                            document.documentElement.setAttribute('data-sidebar', tema.sidebarColor);
                        }
                        if (tema.sidebarSize) {
                            document.documentElement.setAttribute('data-sidebar-size', tema.sidebarSize);
                        }
                        if (tema.sidebarView) {
                            document.documentElement.setAttribute('data-layout-style', tema.sidebarView);
                        }

                        console.log('Tema aplicado antes do Angular:', tema.layoutColor);
                    } catch (e) {
                        console.error('Erro ao aplicar tema:', e);
                    }
                }
            })();
        </script>

        <app-root></app-root>
    </body>
</html>
