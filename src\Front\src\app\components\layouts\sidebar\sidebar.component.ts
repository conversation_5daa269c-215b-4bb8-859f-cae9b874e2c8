import { Component, OnInit, EventEmitter, Output, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { AppGlobals } from "../../../app.globals";
import { ModuloResponse } from 'src/app/models/modulos/modulo';
import { NavigationEnd, Router } from '@angular/router';
import { filter, map } from 'lodash';
import { fromEvent } from 'rxjs';
// import { ModuloResponse } from "../../../models/usuario/usuario-menus";

@Component({
    selector: 'app-sidebar',
    templateUrl: './sidebar.component.html',
    styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent implements OnInit, AfterViewInit {

    logoEstendida = AppGlobals.LOGO_ESTENDIDA;
    logoSm = AppGlobals.LOGO_SM;
    menu: any;
    toggle: any = true;
    menuModulos!: ModuloResponse[];

    @ViewChild('sideMenu') sideMenu!: ElementRef;
    @Output() mobileMenuButtonClicked = new EventEmitter();

    constructor(private router: Router) {
        // this.router.events.pipe(
        //     filter((event: any) => event instanceof NavigationEnd)
        // ).subscribe(() => {
        //     this.initActiveMenu();
        // });
    }

    ngOnInit(): void {
        // Menu Items
        const menuModulosString = localStorage.getItem('MenuModulos');
        if (menuModulosString && menuModulosString !== "null") {
            this.menuModulos = JSON.parse(menuModulosString);
        }
    }

    ngAfterViewInit() {
        this.initActiveMenu();
    }

    activateNavLink(linkId: string) {
        const navLink = document.getElementById(linkId);
        if (navLink) {
            this.deactivateAllNavLinks(); // Remover a classe active de todos os links primeiro
            navLink.classList.add('active');
            navLink.setAttribute('aria-expanded', 'true');
        }
    }

    deactivateAllNavLinks() {
        const navLinks = document.querySelectorAll('.navbar-menu .navbar-nav .nav-link');
        navLinks.forEach((link: any) => {
            link.classList.remove('active');
            link.setAttribute('aria-expanded', 'false');
        });
    }

    initActiveMenu() {
        const pathName = window.location.pathname;
        const ul = document.getElementById("navbar-nav");
        if (ul) {
            const items = Array.from(ul.querySelectorAll("a.nav-link"));
            let activeItems = items.filter((x: any) => x.classList.contains("active"));
            this.removeActivation(activeItems);

            let matchingMenuItem = items.find((x: any) => {
                return x.pathname === pathName;
            });
            if (matchingMenuItem) {
                this.activateParentDropdown(matchingMenuItem);
            }
        }
    }

    activateParentDropdown(item: any) {
        item.classList.add("active");
        let divElementosFilhos = item.closest(".collapse.menu-dropdown");

        if (divElementosFilhos) {
            divElementosFilhos.classList.add("show");
            divElementosFilhos.parentElement.children[0].classList.add("active");
            divElementosFilhos.parentElement.children[0].setAttribute("aria-expanded", "true");
            if (divElementosFilhos.parentElement.closest(".collapse.menu-dropdown")) {
                divElementosFilhos.parentElement.closest(".collapse").classList.add("show");
                if (divElementosFilhos.parentElement.closest(".collapse").previousElementSibling)
                    divElementosFilhos.parentElement.closest(".collapse").previousElementSibling.classList.add("active");
                if (divElementosFilhos.parentElement.closest(".collapse").previousElementSibling.closest(".collapse")) {
                    divElementosFilhos.parentElement.closest(".collapse").previousElementSibling.closest(".collapse").classList.add("show");
                    divElementosFilhos.parentElement.closest(".collapse").previousElementSibling.closest(".collapse").previousElementSibling.classList.add("active");
                }
            }
            return false;
        }
        return false;
    }


    isModuloActive(menuModulo: ModuloResponse): boolean {
        return menuModulo.menuPais?.some(menuPai => this.isMenuPaiActive(menuPai));
    }

    isMenuPaiActive(menuPai: any): boolean {
        return menuPai.menuFilhos?.some((menuFilho: any) => this.router.isActive('/' + menuFilho.link, false)) || this.router.isActive('/' + menuPai.link, false);
    }

    activateModule(event: any) {
        let target = event.target.closest('a.nav-link');
        this.deactivateAll();
        target.classList.add('active');
        target.setAttribute('aria-expanded', 'true');
    }

    deactivateAll() {
        const items = document.querySelectorAll('#navbar-nav .nav-link');
        items.forEach((item: any) => {
            item.classList.remove('active');
            item.setAttribute('aria-expanded', 'false');
            const subMenu = item.nextElementSibling as HTMLElement;
            if (subMenu) {
                subMenu.classList.remove('show');
            }
        });
    }

    updateActive(event: any) {
        let target = event.target.closest('a.nav-link');
        this.activateModule(target);
    }

    toggleMobileMenu(event: any) {
        event.preventDefault();
        this.mobileMenuButtonClicked.emit();
    }

    SidebarHide() {
        const sidebar = document.querySelector('.app-menu');
        if (sidebar) {
            sidebar.classList.remove('show');
        }
        const overlay = document.querySelector('.vertical-overlay');
        if (overlay) {
            overlay.classList.remove('show');
        }
    }

    handleMenuClick(event: Event, menuPai: any): void {
        if (menuPai.menuFilhos && menuPai.menuFilhos.length > 0) {
          // Previne a ação padrão de colapso
          event.preventDefault();
          this.toggleMenuPai(event); // Aqui você controla manualmente o colapso do menu
        }
      }

    toggleMenuModulo(event: Event): void {
        event.preventDefault();
        const target = event.currentTarget as HTMLElement;
        const nextElement = target.nextElementSibling as HTMLElement;
        const isExpanded = nextElement.classList.contains('show');
        if (isExpanded) {
            nextElement.classList.remove('show');
            target.setAttribute('aria-expanded', 'false');
        } else {
            nextElement.classList.add('show');
            target.setAttribute('aria-expanded', 'true');
        }
    }

    toggleMenuPai(event: Event): void {
        event.preventDefault();
        const target = event.currentTarget as HTMLElement;
        const nextElement = target.nextElementSibling as HTMLElement;
        const isExpanded = nextElement.classList.contains('show');
        if (isExpanded) {
            nextElement.classList.remove('show');
            target.setAttribute('aria-expanded', 'false');
        } else {
            nextElement.classList.add('show');
            target.setAttribute('aria-expanded', 'true');
        }
    }


    // toggleMenuModulo(event: Event): void {
    //     event.preventDefault();
    //     const target = event.currentTarget as HTMLElement;
    //     const nextElement = target.nextElementSibling as HTMLElement;
        
    //     // Verifica se existe um submenu
    //     if (nextElement) {
    //         const isExpanded = nextElement.classList.contains('show');
    //         if (isExpanded) {
    //             nextElement.classList.remove('show');
    //             target.setAttribute('aria-expanded', 'false');
    //         } else {
    //             nextElement.classList.add('show');
    //             target.setAttribute('aria-expanded', 'true');
    //         }
    //     }
    // }
    
    // toggleMenuPai(event: Event): void {
    //     event.preventDefault();
    //     const target = event.currentTarget as HTMLElement;
    //     const nextElement = target.nextElementSibling as HTMLElement;
        
    //     if (nextElement) {
    //         const isExpanded = nextElement.classList.contains('show');
    //         if (isExpanded) {
    //             nextElement.classList.remove('show');
    //             target.setAttribute('aria-expanded', 'false');
    //         } else {
    //             nextElement.classList.add('show');
    //             target.setAttribute('aria-expanded', 'true');
    //         }
    //     }
    // }
  

    removeActivation(items: any) {
        let activeItems = items.filter((x: any) => x.classList.contains("active"));
        activeItems.forEach((item: any) => {
            item.classList.remove('active');
            if (item.nextElementSibling && item.nextElementSibling.classList.contains("show")) {
                item.nextElementSibling.classList.remove("show");
                item.setAttribute("aria-expanded", "false");
            }
        });
    }

}
