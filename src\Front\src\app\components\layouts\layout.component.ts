import { Component, OnInit } from '@angular/core';
import { LAYOUT_HORIZONTAL, LAYOUT_TWOCOLUMN, LAYOUT_VERTICAL } from './layout.model';
import { EventService } from "../../services/event/event.service";
import { TemaService } from "../../services/tema/tema.service";
import { UsuarioTemaResponse } from "../../models/tema/tema";

@Component({
    selector: 'app-layout',
    templateUrl: './layout.component.html',
    styleUrls: ['./layout.component.scss']
})

/**
 * Layout Component
 */
export class LayoutComponent implements OnInit {

    layoutStyle!: string;

    constructor(private eventService: EventService,
                private temaService: TemaService) {
    }

    ngOnInit(): void {
        this.eventService.subscribe('changeLayoutStyle', (layout: string) => {
            this.layoutStyle = layout;
        });       
        
        if (!(localStorage.getItem('Tema')))
            this.temaService.getTema();
        else {
            let tema: UsuarioTemaResponse = JSON.parse(<string>localStorage.getItem('Tema'))
            this.layoutStyle = tema.layoutStyle;
            this.temaService.setTema(tema);
        }
    }
    
    /**
     * Check if the vertical layout is requested
     */
    isVerticalLayoutRequested() {
        return this.layoutStyle === LAYOUT_VERTICAL;
    }

    /**
     * Check if the horizontal layout is requested
     */
    isHorizontalLayoutRequested() {
        return this.layoutStyle === LAYOUT_HORIZONTAL;
    }

    /**
     * Check if the horizontal layout is requested
     */
    isTwoColumnLayoutRequested() {
        return this.layoutStyle === LAYOUT_TWOCOLUMN;
    }

}
