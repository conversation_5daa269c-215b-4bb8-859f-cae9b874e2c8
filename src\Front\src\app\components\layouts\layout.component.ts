import { Component, OnInit } from '@angular/core';
import { LAYOUT_HORIZONTAL, LAYOUT_TWOCOLUMN, LAYOUT_VERTICAL } from './layout.model';
import { EventService } from "../../services/event/event.service";
import { TemaService } from "../../services/tema/tema.service";
import { UsuarioTemaResponse } from "../../models/tema/tema";

@Component({
    selector: 'app-layout',
    templateUrl: './layout.component.html',
    styleUrls: ['./layout.component.scss']
})

/**
 * Layout Component
 */
export class LayoutComponent implements OnInit {

    layoutStyle!: string;

    constructor(private eventService: EventService,
                private temaService: TemaService) {
    }

    ngOnInit(): void {
        this.eventService.subscribe('changeLayoutStyle', (layout: string) => {
            this.layoutStyle = layout;
        });       
        
        const temaString = localStorage.getItem('Tema');
        if (!temaString) {
            console.log('LayoutComponent: Nenhum tema no localStorage, buscando tema padrão/backend');
            this.temaService.getTema();
        } else {
            try {
                const tema: UsuarioTemaResponse = JSON.parse(temaString);
                console.log('LayoutComponent: Aplicando tema do localStorage', tema);
                this.layoutStyle = tema.layoutStyle;
                this.temaService.setTema(tema);
            } catch (e) {
                console.error('LayoutComponent: Erro ao parsear tema do localStorage, usando padrão', e);
                this.temaService.getTema();
            }
        }
    }
    
    /**
     * Check if the vertical layout is requested
     */
    isVerticalLayoutRequested() {
        return this.layoutStyle === LAYOUT_VERTICAL;
    }

    /**
     * Check if the horizontal layout is requested
     */
    isHorizontalLayoutRequested() {
        return this.layoutStyle === LAYOUT_HORIZONTAL;
    }

    /**
     * Check if the horizontal layout is requested
     */
    isTwoColumnLayoutRequested() {
        return this.layoutStyle === LAYOUT_TWOCOLUMN;
    }

}
