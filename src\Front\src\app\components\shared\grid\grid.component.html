<div fxFlex="1 0 100%" fxLayout="column" fxLayoutAlign="start stretch">
    <div class="div-flex" fxFlex="0 1 auto">
        <!-- Grid -->
        <div class="mat-sidenav-content" [hidden]="loading">
            <table class="table table-hover"
                   mat-table matSort
                   (matSortChange)="consultarGrid(undefined, $event, undefined)"
                   [dataSource]="data">
                <!-- Cabeçalho e corpo -->
                <ng-container *ngFor="let item of gridOptions.Colunas">
                    <ng-container [matColumnDef]="item.Field">
                        <th [mat-sort-header]="item.Field" class="column-name-header"
                            mat-header-cell *matHeaderCellDef>
                            {{item.DisplayName}}
                        </th>
                        <td mat-cell *matCellDef="let data"
                            [ngStyle]="{'min-width': 'calc(100px + 50px * '+item.Width+')'}">
                            <div *ngIf="item.ActionButton != undefined" class="botao-container">
                                <div class=""
                                     *ngFor="let action of item.ActionButton">
                                    <button [class]="getClassButton(action.ParametrosAction.ClassProperty, data)"
                                            *ngIf="action.TypeButton == 0"
                                            [innerHTML]="getClassButton(action.ParametrosAction.Conteudo, data) | noSanitize"
                                            (click)="exportRow(action, data)"
                                            [hidden]="action.ParametrosAction.Hidden"
                                            [disabled]="action.ParametrosAction.Disabled"
                                            data-toggle="tooltip" data-placement="top"
                                            [title]="action.ParametrosAction.Tooltip">
                                    </button>
                                    <a [hidden]="action.ParametrosAction.Hidden"
                                       [class]="action.ParametrosAction.ClassProperty"
                                       [href]="action.ParametrosAction.Href"
                                       [target]="action.ParametrosAction.Target"
                                       *ngIf="action.TypeButton == 1"
                                       (click)="exportRow(action, data)">
                                    </a>
                                </div>
                            </div>
                            <div *ngIf="item.ActionButton == undefined">
                                <span *ngIf="item.Icon && data[item.Icon]"
                                      class="mdi icon-circle"
                                      [ngClass]="data[item.Icon]">
                                </span>
                                <span *ngIf="item.DotColor && data[item.DotColor]"
                                      class="dot" 
                                      [ngStyle]="{'background-color': data[item.DotColor]}">
                                </span>
                                <!-- <span [innerHTML]="data[item.Field] | noSanitize"></span> -->
                            
                                <img *ngIf="item.Base64 && getNestedProperty(data, item.Base64)" 
                                [src]="getNestedProperty(data, item.Base64)" 
                                class="base64-image"/>

                                <span [innerHTML]="getNestedProperty(data, item.Field) | noSanitize"></span>
                            </div>
                        </td>
                    </ng-container>
                </ng-container>

                <!-- Tipos de input para os filtros -->
                <ng-container *ngFor="let item of gridOptions.Colunas">
                    <ng-container [matColumnDef]="item.ServerField + 'Field'">
                        <th mat-header-cell *matHeaderCellDef class="filter-input-header">
                            <input type="date" class="form-control" *ngIf="item.Filter && item.Type == 'data'" #box
                                   (keyup.enter)="consultarGrid(undefined, undefined, {Value: box.value, Type: item.Type, Field: item.ServerField})">
                            <input type="search" class="form-control" *ngIf="item.Filter && item.Type == 'string'" #box
                                   (keyup.enter)="consultarGrid(undefined, undefined, {Value: box.value, Type: item.Type, Field: item.ServerField})">
                            <input type="number" class="form-control" *ngIf="item.Filter && item.Type == 'number'" #box
                                   (keyup.enter)="consultarGrid(undefined, undefined, {Value: box.value, Type: item.Type, Field: item.ServerField})">
                        </th>
                    </ng-container>
                </ng-container>

                <!-- Header com os nomes das colunas -->
                <tr mat-header-row *matHeaderRowDef="displayedColumns" class="example-first-header-row"></tr>

                <!-- Header com os campos para filtros -->
                <tr mat-header-row *matHeaderRowDef="displayedColumnsFilter" hidden></tr>

                <!-- Linhas com os dados das tabelas -->
                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                
            </table>
        </div>
        <!-- Tela de carregamento -->
        <div class="row" [hidden]="!loading">
            <div class="col-12 text-center pb-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only"></span>
                </div>
            </div>
            <div class="col-12 text-center">
                Carregando dados...
            </div>
        </div>
    </div>
</div>
<mat-paginator class="mt-3"
               [length]="pageEvent.length"
               [hidden]="loading"
               [pageIndex]="pageEvent.pageIndex"
               [pageSize]="pageEvent.pageSize"
               [pageSizeOptions]="pageSizeOptions"
               (page)="consultarGrid($event, undefined, undefined)"
               showFirstLastButtons="true">
</mat-paginator>
