import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { ETypeActionButton } from 'src/app/models/enums/ETypeActionButton';
import { ETypeFilter } from 'src/app/models/enums/ETypeFilter';
import { GridOptions } from 'src/app/models/grid/gridOptions';
import { ActionRow } from "../../../models/grid/ActionRow";
import { EventService } from "../../../services/event/event.service";
import { GridService } from "../../../services/grid/grid.service";
import { BaseService } from 'src/app/services/base/base.service';
import { BaseResponse } from 'src/app/models/base/base-response';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';

import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

@Component({
    selector: 'app-produto-grid',
    templateUrl: './produto.grid.component.html',
    styleUrls: ['./produto.grid.component.scss']
})

export class ProdutoGridComponent implements OnInit, OnDestroy {
    gridOptions!: GridOptions; // Adicione o operador ! para indicar que será inicializado no ngOnInit
    breadCrumbItems!: Array<{}>;
    gridId = 'produto-grid';
    private subscriptions: Subscription[] = [];
    
    constructor(private gridService: GridService,
                private eventService: EventService,
                private baseService: BaseService,
                private toastr: ToastrService,
                private matDatepickerModule: MatDatepickerModule,
                private matNativeDateModule: MatNativeDateModule,
                private matFormFieldModule: MatFormFieldModule,
                private matInputModule: MatInputModule,
                private router: Router) {
        
    }
    
    ngOnInit(): void {
        this.breadCrumbItems = [
            {label: 'Dashboards'},
            {label: 'Produto', active: true}
        ];

        const subscription = this.eventService.subscribe('gridActionRow', (action: ActionRow) => {
            if (action.Action.TypeActionButton == ETypeActionButton.Editar)
                this.router.navigate(['produto/editar', action.Data.id]);

            if (action.Action.TypeActionButton == ETypeActionButton.Inativar)
                this.alterarStatus(action.Data.id);
        });
        
        this.subscriptions.push(subscription);
        
        this.gridOptions = {
            Parametros: {
                Controller: 'Produto',
                Metodo: 'ConsultarGrid',
                PaginatorSizeOptions: [10, 50, 100],
                PageSize: 10,
            },
            Colunas: [
                {
                    Field: 'Ações',
                    DisplayName: 'Ações',
                    Width: 0,
                    CellTemplate: undefined,
                    Type: ETypeFilter.none,
                    Filter: false,
                    ServerField: '',
                    ActionButton: [
                        {
                            TypeActionButton: ETypeActionButton.Editar,
                            TypeButton: 0,
                            ParametrosAction: {
                                Conteudo: '<i class="bx bx-pencil" style="font-size: 15px;"></i>',
                                ClassProperty: 'btn btn-primary btn-sm',
                                Disabled: false,
                                Hidden: false,
                                Target: undefined,
                                Href: undefined,
                                Tooltip: 'Editar'
                            }
                        },
                        {
                            TypeActionButton: ETypeActionButton.Inativar,
                            TypeButton: 0,
                            ParametrosAction: {
                                Conteudo: (data) => data.ativo === 'Inativo'? '<i class="bx bx-block" style="font-size: 15px;"></i>' 
                                : '<i class="bx bx-check" style="font-size: 15px;"></i>',
                                ClassProperty: (data: any) => data.ativo === 'Inativo' ? 'btn btn-warning btn-sm' : 'btn btn-success btn-sm',
                                Disabled: false,
                                Hidden: false,
                                Target: undefined,
                                Href: undefined,
                                Tooltip: 'Alterar Status'
                            },
                        }
                    ]
                },
                {
                    Field: 'id',
                    DisplayName: 'Cód',
                    CellTemplate: undefined,
                    Width: 0,
                    ActionButton: undefined,
                    Type: ETypeFilter.Number,
                    Filter: false,
                    ServerField: 'Id'
                },
                {
                    Field: 'nome',
                    DisplayName: 'Nome',
                    Width: 3,
                    CellTemplate: undefined,
                    ActionButton: undefined,
                    Type: ETypeFilter.String,
                    Filter: true,
                    ServerField: 'nome',
                    Base64: 'foto'
                },
                {
                    Field: 'ativo',
                    DisplayName: 'Status',
                    Width: 2,
                    CellTemplate: undefined,
                    ActionButton: undefined,
                    Type: ETypeFilter.String,
                    ServerField: 'ativo',
                    Filter: false
                },

                {
                    Field: 'empresaResponse.nomeFantasia',
                    DisplayName: 'Empresa',
                    Width: 2,
                    CellTemplate: undefined,
                    ActionButton: undefined,
                    Type: ETypeFilter.String,
                    ServerField: 'empresaResponse.nomeFantasia',
                    Filter: false,
                    Base64: 'empresaResponse.foto'
                },

                {
                    Field: 'dataCadastro',
                    DisplayName: 'Data Cadastro',
                    Width: 2,
                    CellTemplate: undefined,
                    ActionButton: undefined,
                    Type: ETypeFilter.String,
                    ServerField: 'dataCadastro',
                    Filter: false
                },
                {
                    Field: 'usuarioCadastro.nome',
                    DisplayName: 'Usuário Cadastro',
                    Width: 2,
                    CellTemplate: undefined,
                    ActionButton: undefined,
                    Type: ETypeFilter.String,
                    ServerField: 'usuarioCadastro.nome',
                    Filter: false,
                    Base64: 'usuarioCadastro.foto'
                },
                {
                    Field: 'dataAlteracao',
                    DisplayName: 'Data Alteracao',
                    Width: 2,
                    CellTemplate: undefined,
                    ActionButton: undefined,
                    Type: ETypeFilter.String,
                    ServerField: 'dataAlteracao',
                    Filter: false
                },
                {
                    Field: 'usuarioAlteracao.nome',
                    DisplayName: 'Usuário Alteracao',
                    Width: 2,
                    CellTemplate: undefined,
                    ActionButton: undefined,
                    Type: ETypeFilter.String,
                    ServerField: 'usuarioAlteracao.nome',
                    Filter: false,
                    Base64: 'usuarioAlteracao.foto'
                }
            ]
        }
    }
    
    ngOnDestroy(): void {
        console.log(`ProdutoGridComponent sendo destruído, cancelando ${this.subscriptions.length} inscrições`);

        // Cancele todas as inscrições quando o componente for destruído
        this.subscriptions.forEach((subscription, index) => {
            if (subscription && !subscription.closed) {
                subscription.unsubscribe();
                console.log(`Inscrição ${index} do ProdutoGrid cancelada`);
            }
        });

        // Limpe o array de inscrições
        this.subscriptions = [];
    }
    
    alterarStatus(cod: String): void {
        const subscription = this.baseService.Post("Produto", "AlterarStatus?Cod=" + cod).subscribe(
            (response: BaseResponse) => {
                if (response.success) {
                    this.recarregarGrid();
                    this.toastr.success(response.message, 'Mensagem');
                } else {
                    this.toastr.error(response.message, 'Mensagem');
                }
            },
            (error: any) => {
                this.toastr.error(error.message, 'Mensagem');
            }
        );

        // Adicionar a subscription para ser cancelada no ngOnDestroy
        this.subscriptions.push(subscription);
    }

    recarregarGrid() {
        this.gridService.recarregarGrid(this.gridId);
    }
}
