import { Component, OnInit, Input, ElementRef, EventEmitter, OnDestroy } from '@angular/core';
import { Subscription, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { MatPaginatorIntl, PageEvent } from '@angular/material/paginator';
import { Action, GridOptions } from 'src/app/models/grid/gridOptions';
import { Sort } from '@angular/material/sort';
import { BaseService } from "../../../services/base/base.service";
import { DomSanitizer } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { ResponseData } from 'src/app/models/grid/ResponseData';
import { Filter } from 'src/app/models/grid/Filter';
import { EventService } from "../../../services/event/event.service";
import { GridService } from "../../../services/grid/grid.service";

@Component({
    selector: 'app-grid',
    templateUrl: './grid.component.html',
    styleUrls: ['./grid.component.scss']
})

export class GridComponent implements OnInit, OnDestroy {
    @Input() gridOptions!: GridOptions;
    @Input() gridId?: string;

    // Adicione uma propriedade para armazenar as inscrições
    private subscriptions: Subscription[] = [];

    // Controle de requisições pendentes e debounce
    private isRequestPending = false;
    private reloadSubject = new Subject<void>();

    //variaveis grid
    displayedColumnsHeader: string[] = [];
    displayedColumnsFilter: string[] = [];
    displayedColumns: string[] = [];
    data: any = [];
    loading = true;
    IdGrupoEconomico?: number | undefined
    isDialog: boolean = false

    //variaveis paginação
    pageEvent: PageEvent = {
        pageIndex: 0,
        pageSize: 10,
        length: 10,
    }
    pageSizeOptions: number[] = [5, 10, 25, 100];

    //Variaveis Ordenação
    sort: Sort = {
        active: '',
        direction: ''
    }
    
    QueryFilters: Filter[] = [];

    constructor(private response: BaseService,
                private toastr: ToastrService,
                private router: Router,
                private paginator: MatPaginatorIntl,
                private elementRef: ElementRef,
                private sanitizer: DomSanitizer,
                private gridService: GridService,
                private eventService: EventService) {

        paginator.itemsPerPageLabel = 'Itens por página'

        // Configurar debounce para recarregamento da grid
        const reloadSubscription = this.reloadSubject.pipe(
            debounceTime(300), // Aguarda 300ms antes de executar
            distinctUntilChanged()
        ).subscribe(() => {
            this.executeConsultarGrid();
        });

        this.subscriptions.push(reloadSubscription);
    }

    ngOnInit(): void {
        //Setar colunas e configurações da grid
        this.gridOptions.Colunas.forEach(element => {
            this.displayedColumns.push(element.Field);
            this.displayedColumnsFilter.push(element.ServerField + 'Field');
        });

        if (this.gridOptions.Parametros.PaginatorSizeOptions != undefined)
            this.pageSizeOptions = this.gridOptions.Parametros.PaginatorSizeOptions;

        if (this.gridOptions.Parametros.PageSize != undefined)
            this.pageEvent.pageSize = this.gridOptions.Parametros.PageSize;


        if (this.gridOptions.Parametros.IdGrupoEconomico != undefined)
            this.IdGrupoEconomico = this.gridOptions.Parametros.IdGrupoEconomico;


        if (this.gridOptions.Parametros.IdGrupoEconomico != undefined)
            this.IdGrupoEconomico = this.gridOptions.Parametros.IdGrupoEconomico;

        if (this.gridOptions.Parametros.isDialog != null) {
            this.isDialog = this.gridOptions.Parametros.isDialog;
        }

        // Inscreva-se no evento específico da grid
        const eventName = this.gridId ? `gridRecarregar_${this.gridId}` : 'gridRecarregar';
        console.log(`Grid ${this.gridId || 'sem ID'} inscrita no evento: ${eventName}`);
        
        const subscription = this.eventService.subscribe(eventName, () => {
            console.log(`Evento ${eventName} recebido, recarregando grid ${this.gridId || 'sem ID'}`);
            this.consultarGrid(undefined, undefined, undefined);
        });
        
        // Armazene a inscrição para cancelá-la mais tarde
        this.subscriptions.push(subscription);
        
        // Mantenha a inscrição legada apenas se não tiver gridId
        if (!this.gridId) {
            const legacySubscription = this.eventService.subscribe('gridRecarregar', () => {
                this.consultarGrid(undefined, undefined, undefined);
            });
            this.subscriptions.push(legacySubscription);
        }

        this.consultarGrid(this.pageEvent);
    }

    ngOnDestroy(): void {
        console.log(`Grid ${this.gridId || 'sem ID'} sendo destruída, cancelando ${this.subscriptions.length} inscrições`);

        // Cancele todas as inscrições quando o componente for destruído
        this.subscriptions.forEach((subscription, index) => {
            if (subscription && !subscription.closed) {
                subscription.unsubscribe();
                console.log(`Inscrição ${index} cancelada`);
            }
        });

        // Limpe o array de inscrições
        this.subscriptions = [];

        // Complete o subject para evitar vazamentos de memória
        if (this.reloadSubject) {
            this.reloadSubject.complete();
        }
    }

    getNestedProperty(obj: any, path: string): any {
        return path.split('.').reduce((prev, curr) => (prev ? prev[curr] : null), obj);
    }

    getClassButton(classProperty: string | ((data: any) => string) | undefined, data: any): string {
        if (typeof classProperty === 'function') {
            return classProperty(data);
        }
        return classProperty ?? '';
    }

    consultarGrid(event?: PageEvent, sortEvent?: Sort, filter?: Filter) {
        // Evitar múltiplas requisições simultâneas
        if (this.isRequestPending) {
            console.log(`Grid ${this.gridId || 'sem ID'}: Requisição já em andamento, ignorando nova chamada`);
            return;
        }

        console.log(`Grid ${this.gridId || 'sem ID'}: Iniciando consultarGrid`);
        this.isRequestPending = true;
        this.loading = true;

        if (event != undefined)
            this.pageEvent = event;

        if (sortEvent != undefined)
            this.sort = sortEvent;

        if (filter != undefined) {

            for (let index = 0; index < this.QueryFilters.length; index++) {
                if (this.QueryFilters[index].Field == filter.Field)
                    this.QueryFilters.splice(index, 1);
            }

            if (filter.Value != "") {
                this.QueryFilters.push(filter);
            }
        }

        this.response.Post(this.gridOptions.Parametros.Controller, this.gridOptions.Parametros.Metodo, {
            Take: this.pageEvent.pageSize,
            Page: this.pageEvent.pageIndex + 1,
            Order: {
                Campo: this.sort.active,
                Operador: this.sort.direction == 'asc' ? 0 : 1
            },
            QueryFilters: this.QueryFilters,
            IdGrupoEconomico: this.IdGrupoEconomico
        })
            .subscribe(
                (response: ResponseData) => {
                    console.log(`Grid ${this.gridId || 'sem ID'}: Resposta recebida`);

                    if (response.success) {
                        this.data = response.data.itens;
                        this.pageEvent.length = response.data.totalItens;

                        //Atribuição de html a coluna
                        this.gridOptions.Colunas.forEach(element => {
                            if (element.ActionButton != undefined) {
                                response.data.itens.forEach(cell =>
                                    cell[element.Field] = element.CellTemplate);
                            }
                        });

                        //Atribuição de action button a coluna
                        this.gridOptions.Colunas.forEach(element => {
                            if (element.CellTemplate != undefined) {
                                response.data.itens.forEach(cell =>
                                    cell[element.Field] = element.CellTemplate);
                            }
                        });
                        this.loading = false;
                        this.sendBroadcastTotalItens(response.data.totalItens)
                    } else {
                        this.loading = false;
                        this.sendBroadcastTotalItens(response.data.totalItens)
                        this.toastr.error(response.message, 'Mensagem');
                    }

                    // Resetar flag de requisição pendente
                    this.isRequestPending = false;
                },
                (error) => {
                    console.error(`Grid ${this.gridId || 'sem ID'}: Erro na requisição`, error);
                    this.loading = false;
                    this.isRequestPending = false;
                    this.toastr.error('Erro ao carregar dados da grid', 'Erro');
                }
            );
    }

    sendBroadcastTotalItens(total: number) {
        if (!this.isDialog) {
            this.gridService.totalItensGrid(total);
        } else {
            this.gridService.totalItensDialog(total);
        }
    }

    //Grid Services
    exportRow(action: Action, data: any) {
        this.gridService.getActionRowGrid({
            Data: data,
            Action: action
        });
    }
}
